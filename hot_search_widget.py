#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热搜监控组件 - 高性能版本
重构为可嵌入的Widget组件
"""

import sys
import requests
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLabel, QHeaderView,
                            QMessageBox, QProgressBar, QGroupBox, QCheckBox,
                            QSpinBox, QComboBox)
from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal, QMutex
from PyQt5.QtGui import QFont, QColor
from datetime import datetime
import json
import os


class HotSearchAPI(QThread):
    """热搜API异步请求类"""
    
    data_received = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.params = {
            "a": "GetHotSearch",
            "apiv": "w31",
            "Type": "1",
            "c": "InteractData",
            "PhoneOSNew": "1",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******"
        }
        self.mutex = QMutex()
        self.running = False
        
    def run(self):
        """执行API请求"""
        self.mutex.lock()
        try:
            if not self.running:
                return
                
            response = requests.get(self.base_url, params=self.params, 
                                  headers=self.headers, timeout=10)
            response.raise_for_status()
            data = response.json().get('List', [])
            self.data_received.emit(data)
            
        except requests.exceptions.RequestException as e:
            self.error_occurred.emit(f"网络请求错误: {str(e)}")
        except json.JSONDecodeError as e:
            self.error_occurred.emit(f"数据解析错误: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")
        finally:
            self.mutex.unlock()
            
    def start_request(self):
        """开始请求"""
        self.running = True
        if not self.isRunning():
            self.start()
            
    def stop_request(self):
        """停止请求"""
        self.running = False
        if self.isRunning():
            self.quit()
            self.wait(3000)  # 等待最多3秒


class HotSearchWidget(QWidget):
    """热搜监控Widget组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = HotSearchAPI()
        self.timer = QTimer()
        self.data_cache = []
        self.last_update_time = None
        self.auto_refresh_enabled = False

        # 集成数据管理器
        try:
            from data_manager import get_hot_search_manager
            self.data_manager = get_hot_search_manager()
            self.use_data_manager = True
        except ImportError:
            self.data_manager = None
            self.use_data_manager = False

        self.init_ui()
        self.setup_connections()
        self.load_initial_data()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 导入主题
        try:
            from modern_theme import ModernTheme
            theme_available = True
        except ImportError:
            theme_available = False

        # 页面标题
        title_widget = self.create_page_title()
        layout.addWidget(title_widget)

        # 控制面板卡片
        control_card = QGroupBox("⚙️ 控制面板")
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 手动刷新")
        self.refresh_btn.setMinimumHeight(40)
        self.refresh_btn.setProperty("class", "success")

        # 自动刷新控件组
        auto_refresh_group = self.create_auto_refresh_group()

        # 导出按钮
        self.export_btn = QPushButton("📤 导出数据")
        self.export_btn.setMinimumHeight(40)
        self.export_btn.setProperty("class", "primary")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMinimumHeight(6)

        # 布局控制面板
        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(auto_refresh_group)
        control_layout.addWidget(self.export_btn)
        control_layout.addStretch()
        control_card.setLayout(control_layout)

        # 状态信息卡片
        status_card = self.create_status_card()

        # 数据表格
        self.table = QTableWidget()
        self.setup_table()

        # 组装布局
        layout.addWidget(control_card)
        layout.addWidget(status_card)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.table)

    def create_page_title(self):
        """创建页面标题"""
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)

        # 主标题
        title_label = QLabel("🔥 热搜监控")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 700;
                color: #FF5722;
                margin: 0;
            }
        """)

        # 描述
        desc_label = QLabel("实时监控热搜关键词数据变化")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #757575;
                margin-left: 10px;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        title_layout.addStretch()

        return title_widget

    def create_auto_refresh_group(self):
        """创建自动刷新控件组"""
        from PyQt5.QtWidgets import QCheckBox, QSpinBox

        group_widget = QWidget()
        group_layout = QHBoxLayout(group_widget)
        group_layout.setContentsMargins(0, 0, 0, 0)
        group_layout.setSpacing(10)

        # 自动刷新复选框
        self.auto_refresh_cb = QCheckBox("🔄 自动刷新")

        # 间隔设置
        interval_label = QLabel("间隔:")
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(10, 300)
        self.interval_spinbox.setValue(30)
        self.interval_spinbox.setSuffix("s")
        self.interval_spinbox.setMinimumWidth(80)

        group_layout.addWidget(self.auto_refresh_cb)
        group_layout.addWidget(interval_label)
        group_layout.addWidget(self.interval_spinbox)

        return group_widget

    def create_status_card(self):
        """创建状态信息卡片"""
        status_card = QGroupBox("📊 实时状态")
        status_layout = QHBoxLayout()
        status_layout.setSpacing(20)

        # 状态指示器
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 12px;
                background-color: #E8F5E8;
                border-radius: 6px;
                color: #2E7D32;
            }
        """)

        # 数据统计
        self.data_count_label = QLabel("📈 数据条数: 0")
        self.data_count_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 12px;
                background-color: #E3F2FD;
                border-radius: 6px;
                color: #1976D2;
            }
        """)

        # 更新时间
        self.update_time_label = QLabel("🕒 最后更新: --")
        self.update_time_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 12px;
                background-color: #FFF3E0;
                border-radius: 6px;
                color: #F57C00;
            }
        """)

        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.data_count_label)
        status_layout.addWidget(self.update_time_label)
        status_layout.addStretch()

        status_card.setLayout(status_layout)
        return status_card
        
    def setup_table(self):
        """设置数据表格"""
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["🔥 关键词", "📊 热度值", "📈 排位变化", "🏆 实时排名"])

        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSortingEnabled(True)

        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 关键词列自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 热度值列
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 排位变化列
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 实时排名列

        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(50)
        self.table.verticalHeader().setVisible(False)

        # 添加表格阴影效果
        try:
            from modern_theme import ModernEffects
            ModernEffects.add_shadow(self.table, blur_radius=10, offset=(0, 2))
        except ImportError:
            pass
        
    def setup_connections(self):
        """设置信号连接"""
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.auto_refresh_cb.toggled.connect(self.toggle_auto_refresh)
        self.interval_spinbox.valueChanged.connect(self.update_refresh_interval)
        self.export_btn.clicked.connect(self.export_data)
        
        # API信号连接
        self.api.data_received.connect(self.on_data_received)
        self.api.error_occurred.connect(self.on_error_occurred)
        
        # 定时器连接
        self.timer.timeout.connect(self.refresh_data)
        
    def load_initial_data(self):
        """加载初始数据"""
        self.refresh_data()
        
    def refresh_data(self):
        """刷新数据"""
        if self.use_data_manager and self.data_manager:
            # 使用数据管理器
            self.status_label.setText("正在获取数据...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.refresh_btn.setEnabled(False)

            self.data_manager.update_data(force_refresh=True)
        else:
            # 使用原始API
            if self.api.isRunning():
                return

            self.status_label.setText("正在获取数据...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.refresh_btn.setEnabled(False)

            self.api.start_request()
        
    def on_data_received(self, data):
        """处理接收到的数据"""
        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)

        if data:
            self.data_cache = data
            self.update_table(data)
            self.last_update_time = datetime.now()

            # 更新状态显示
            self.status_label.setText("🟢 数据更新成功")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: 500;
                    padding: 8px 12px;
                    background-color: #E8F5E8;
                    border-radius: 6px;
                    color: #2E7D32;
                }
            """)

            self.update_time_label.setText(f"🕒 最后更新: {self.last_update_time.strftime('%H:%M:%S')}")
            self.data_count_label.setText(f"📈 数据条数: {len(data)}")
        else:
            self.status_label.setText("🔴 未获取到数据")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: 500;
                    padding: 8px 12px;
                    background-color: #FFEBEE;
                    border-radius: 6px;
                    color: #C62828;
                }
            """)
            
    def on_error_occurred(self, error_msg):
        """处理错误"""
        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)

        # 更新错误状态显示
        self.status_label.setText(f"❌ 错误: {error_msg}")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 12px;
                background-color: #FFEBEE;
                border-radius: 6px;
                color: #C62828;
            }
        """)
        
    def update_table(self, data):
        """更新表格数据"""
        self.table.setRowCount(len(data))
        
        # 按热度值排序
        sorted_data = sorted(data, key=lambda x: x.get('Hot', 0), reverse=True)
        rank_dict = {item['KeyWord']: idx + 1 for idx, item in enumerate(sorted_data)}
        
        for row_idx, item in enumerate(data):
            # 关键词
            keyword_item = QTableWidgetItem(item.get('KeyWord', ''))
            keyword_item.setFlags(keyword_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row_idx, 0, keyword_item)
            
            # 热度值
            hot_item = QTableWidgetItem()
            hot_value = int(item.get('Hot', 0))
            hot_item.setData(Qt.DisplayRole, hot_value)
            hot_item.setFlags(hot_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row_idx, 1, hot_item)
            
            # 排位变化
            order_item = QTableWidgetItem()
            order_value = int(item.get('Order', 0))
            order_item.setData(Qt.DisplayRole, order_value)
            order_item.setFlags(order_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row_idx, 2, order_item)
            
            # 实时排名
            rank = rank_dict.get(item.get('KeyWord', ''), 0)
            rank_item = QTableWidgetItem()
            rank_item.setData(Qt.DisplayRole, rank)
            rank_item.setFlags(rank_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row_idx, 3, rank_item)
            
            # 设置行颜色和样式（根据热度值）
            if hot_value > 1000000:  # 超过100万的热度用特殊样式
                for col in range(4):
                    item = self.table.item(row_idx, col)
                    if item:
                        item.setBackground(QColor("#FFEBEE"))  # 浅红色背景
                        item.setForeground(QColor("#C62828"))  # 深红色文字
                        if col == 0:  # 关键词列添加火焰图标
                            item.setText(f"🔥 {item.text()}")
            elif hot_value > 500000:  # 50万以上用橙色
                for col in range(4):
                    item = self.table.item(row_idx, col)
                    if item:
                        item.setBackground(QColor("#FFF3E0"))
                        item.setForeground(QColor("#F57C00"))
                        if col == 0:
                            item.setText(f"⭐ {item.text()}")
            elif rank <= 3:  # 前三名用金色
                for col in range(4):
                    item = self.table.item(row_idx, col)
                    if item:
                        item.setBackground(QColor("#FFFDE7"))
                        item.setForeground(QColor("#F9A825"))
                        if col == 0:
                            medals = ["🥇", "🥈", "🥉"]
                            item.setText(f"{medals[rank-1]} {item.text()}")
                        
    def toggle_auto_refresh(self, enabled):
        """切换自动刷新"""
        self.auto_refresh_enabled = enabled

        if enabled:
            if self.use_data_manager and self.data_manager:
                # 使用数据管理器的自动更新
                interval = self.interval_spinbox.value()
                self.data_manager.start_auto_update(interval)
                self.status_label.setText("自动刷新已启用（数据管理器）")
            else:
                # 使用原始定时器
                interval = self.interval_spinbox.value() * 1000  # 转换为毫秒
                self.timer.start(interval)
                self.status_label.setText("自动刷新已启用")
        else:
            if self.use_data_manager and self.data_manager:
                self.data_manager.stop_auto_update()
            else:
                self.timer.stop()
            self.status_label.setText("自动刷新已停用")
            
    def update_refresh_interval(self, value):
        """更新刷新间隔"""
        if self.auto_refresh_enabled:
            self.timer.stop()
            self.timer.start(value * 1000)
            
    def export_data(self):
        """导出数据"""
        if not self.data_cache:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return
            
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            filename = os.path.join(current_dir, 
                                  f"hotsearch_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            
            with open(filename, 'w', encoding='utf-8-sig') as f:
                f.write("关键词,热度值,排位变化,实时排名\n")
                
                # 按热度值排序
                sorted_data = sorted(self.data_cache, key=lambda x: x.get('Hot', 0), reverse=True)
                rank_dict = {item['KeyWord']: idx + 1 for idx, item in enumerate(sorted_data)}
                
                for item in self.data_cache:
                    keyword = item.get('KeyWord', '')
                    hot = item.get('Hot', 0)
                    order = item.get('Order', 0)
                    rank = rank_dict.get(keyword, 0)
                    f.write(f'"{keyword}",{hot},{order},{rank}\n')
                    
            QMessageBox.information(self, "导出成功", f"数据已保存到:\n{filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出失败:\n{str(e)}")
            
    def stop_timers(self):
        """停止所有定时器"""
        self.timer.stop()
        self.api.stop_request()
        
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_timers()
        event.accept()
