#!/usr/bin/env python3
"""
修复PyInstaller DLL依赖问题的构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """清理之前的构建文件"""
    print("清理构建文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")
    
    # 删除spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"已删除文件: {spec_file}")

def install_dependencies():
    """安装必要的依赖包"""
    print("安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=6.0"])
        print("依赖包安装完成!")
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False
    return True

def build_hotsearch_app():
    """构建热搜监控应用"""
    print("构建热搜监控应用...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name", "热搜监控",
        "--noconfirm",
        "--clean",
        # 排除不必要的模块
        "--exclude-module", "pandas",
        "--exclude-module", "IPython", 
        "--exclude-module", "sphinx",
        "--exclude-module", "matplotlib",
        "--exclude-module", "numpy",
        # 添加必要的隐藏导入
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "PyQt5.QtWidgets", 
        "--hidden-import", "requests",
        "--hidden-import", "json",
        "--hidden-import", "datetime",
        # 收集所有PyQt5相关文件
        "--collect-all", "PyQt5",
        "HotSearchApp.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("热搜监控应用构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"热搜监控应用构建失败: {e}")
        return False

def build_stock_app():
    """构建股票L2监控应用"""
    print("构建股票L2监控应用...")
    
    cmd = [
        "pyinstaller",
        "--onefile", 
        "--windowed",
        "--name", "股票L2监控",
        "--noconfirm",
        "--clean",
        # 排除问题模块
        "--exclude-module", "pandas",
        "--exclude-module", "IPython",
        "--exclude-module", "sphinx", 
        "--exclude-module", "docutils",
        "--exclude-module", "babel",
        "--exclude-module", "zmq",
        "--exclude-module", "tornado",
        "--exclude-module", "jupyter",
        # 添加必要的隐藏导入
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui", 
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "requests",
        "--hidden-import", "matplotlib.backends.backend_qt5agg",
        "--hidden-import", "matplotlib.figure",
        "--hidden-import", "numpy",
        # 收集必要的包
        "--collect-all", "PyQt5",
        "--collect-all", "matplotlib",
        "Da Dan L2 v2.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("股票L2监控应用构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"股票L2监控应用构建失败: {e}")
        return False

def create_portable_version():
    """创建便携版本"""
    print("创建便携版本...")
    
    # 创建便携版目录
    portable_dir = Path("portable_version")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    portable_dir.mkdir()
    
    # 复制exe文件
    dist_dir = Path("dist")
    if dist_dir.exists():
        for exe_file in dist_dir.glob("*.exe"):
            shutil.copy2(exe_file, portable_dir)
            print(f"已复制: {exe_file.name}")
    
    # 复制说明文件
    docs = ["README.md", "运行说明.txt"]
    for doc in docs:
        if os.path.exists(doc):
            shutil.copy2(doc, portable_dir)
            print(f"已复制: {doc}")
    
    print(f"便携版已创建在: {portable_dir}")

def main():
    """主函数"""
    print("=== 修复版Python应用打包工具 ===")
    print("正在修复DLL依赖问题...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return
    
    # 清理构建文件
    clean_build()
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 构建应用
    success_count = 0
    
    if build_hotsearch_app():
        success_count += 1
    
    if build_stock_app():
        success_count += 1
    
    if success_count > 0:
        create_portable_version()
        print(f"\n=== 构建完成! 成功构建 {success_count} 个应用 ===")
        print("可执行文件位置:")
        print("- dist/ 目录中的原始文件")
        print("- portable_version/ 目录中的便携版本")
        print("\n如果仍有DLL问题，请尝试在目标机器上安装Visual C++ Redistributable")
    else:
        print("\n=== 构建失败! ===")

if __name__ == "__main__":
    main()
