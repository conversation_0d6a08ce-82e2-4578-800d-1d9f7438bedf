# 多阶段构建，减小镜像体积
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译程序
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o stock-monitor .

# 运行阶段 - 使用最小的基础镜像
FROM scratch

# 从builder阶段复制必要文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /app/stock-monitor /stock-monitor
COPY --from=builder /app/templates /templates

# 设置时区
ENV TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8080

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/stock-monitor", "health"] || exit 1

# 运行程序
ENTRYPOINT ["/stock-monitor"]
