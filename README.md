# 股票监控应用 - Windows可执行版本

## 应用介绍

本项目包含两个主要的股票监控应用，已打包为Windows可执行文件，无需安装Python环境即可直接运行。

### 应用列表

1. **热搜监控.exe** - 热搜数据监控应用
   - 实时获取热搜数据
   - 表格形式展示关键词、热度值、排位变化等信息
   - 支持自动刷新功能（10秒间隔）
   - 支持数据导出为CSV格式

2. **股票L2监控.exe** - 股票L2大单数据监控应用
   - 实时监控指定股票的L2大单净额数据
   - 图表可视化显示数据趋势
   - 支持多种刷新间隔（5秒-5分钟）
   - 实时数据分析和统计
   - 支持数据和图表保存

## 使用方法

### 直接运行
1. 双击 `dist/热搜监控.exe` 启动热搜监控应用
2. 双击 `dist/股票L2监控.exe` 启动股票L2监控应用

### 功能说明

#### 热搜监控应用
- **手动刷新**: 点击"手动刷新"按钮立即更新数据
- **自动刷新**: 点击"开启自动刷新"按钮启动10秒自动刷新
- **数据导出**: 点击"导出数据"按钮将当前数据保存为CSV文件
- **排序功能**: 点击表格列标题可按该列排序

#### 股票L2监控应用
- **股票代码**: 输入6位股票代码（如：002334）
- **刷新间隔**: 选择数据刷新间隔（5秒、10秒、30秒、1分钟、5分钟）
- **开始监控**: 点击"▶ 开始监控"开始实时监控
- **停止监控**: 点击"■ 停止"停止监控
- **保存数据**: 点击"💾 保存数据"保存当前数据和图表

## 性能优化

### 已实现的优化
1. **路径优化**: 移除硬编码路径，使用相对路径
2. **字体优化**: 智能字体检测，支持多种中文字体
3. **资源优化**: 优化导入和资源使用
4. **打包优化**: 使用PyInstaller优化打包配置

### 系统要求
- Windows 7/8/10/11 (64位)
- 至少 100MB 可用磁盘空间
- 网络连接（用于获取实时数据）

## 文件说明

### 可执行文件
- `dist/热搜监控.exe` - 热搜监控应用（约30-50MB）
- `dist/股票L2监控简化版.exe` - 股票L2监控应用（约50-80MB，优化版本）

### 源代码文件
- `HotSearchApp.py` - 热搜监控应用源码
- `Da Dan L2 v2.py` - 股票L2监控应用源码（优化版）
- `Da Dan L2.py` - 股票L2监控应用源码（原版）

### 配置文件
- `requirements.txt` - Python依赖包列表
- `build_config.py` - 自动化打包配置脚本
- `build.bat` - Windows批处理打包脚本

## 开发信息

### 技术栈
- **GUI框架**: PyQt5
- **HTTP请求**: requests
- **数据可视化**: matplotlib
- **数据处理**: numpy
- **打包工具**: PyInstaller

### 重新构建
如需重新构建可执行文件：

1. 安装Python 3.7+
2. 运行 `build.bat` 或执行 `python build_config.py`
3. 生成的exe文件将位于 `dist/` 目录

## 注意事项

1. **防火墙**: 首次运行时可能需要允许应用通过防火墙
2. **杀毒软件**: 某些杀毒软件可能误报，请添加信任
3. **网络**: 应用需要网络连接获取实时数据
4. **数据保存**: 导出的数据文件保存在应用同目录下

## 更新日志

### v1.0 (当前版本)
- 完成应用打包为Windows可执行文件
- 优化文件路径处理
- 改进字体兼容性
- 添加自动化构建脚本

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 防火墙设置是否阻止应用
3. 系统是否满足最低要求

---

**注意**: 本应用仅供学习和研究使用，请遵守相关法律法规和API使用条款。
