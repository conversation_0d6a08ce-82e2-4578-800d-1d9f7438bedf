#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一监控应用启动脚本
高性能版本的主入口
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt5
    except ImportError:
        missing_packages.append("PyQt5")
        
    try:
        import requests
    except ImportError:
        missing_packages.append("requests")
        
    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")
        
    try:
        import matplotlib
    except ImportError:
        missing_packages.append("matplotlib")
        
    return missing_packages

def show_dependency_error(missing_packages):
    """显示依赖包错误"""
    app = QApplication(sys.argv)
    
    message = "缺少以下依赖包，请先安装：\n\n"
    for package in missing_packages:
        message += f"- {package}\n"
    
    message += "\n安装命令：\n"
    message += f"pip install {' '.join(missing_packages)}"
    
    QMessageBox.critical(None, "依赖包错误", message)
    sys.exit(1)

def main():
    """主函数"""
    print("=" * 50)
    print("统一监控应用 - 高性能版本")
    print("=" * 50)
    
    # 检查依赖包
    missing_packages = check_dependencies()
    if missing_packages:
        show_dependency_error(missing_packages)
        
    try:
        # 导入主应用和主题
        from unified_monitoring_app import UnifiedMonitoringApp, create_splash_screen

        # 创建应用实例
        app = QApplication(sys.argv)
        app.setApplicationName("统一监控应用")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("MonitoringApp")

        # 启用高DPI支持（如果支持的话）
        try:
            if hasattr(Qt, 'AA_EnableHighDpiScaling'):
                app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
                app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            pass  # 忽略不支持的属性

        # 应用现代化主题
        try:
            from modern_theme import apply_modern_theme
            apply_modern_theme(app)
            print("✅ 现代化主题已应用")
        except ImportError:
            # 如果主题模块不可用，使用默认样式
            app.setStyle("Fusion")
            print("⚠️ 使用默认主题")
        
        print("正在启动应用...")
        
        # 显示启动画面
        splash = create_splash_screen()
        splash.show()
        app.processEvents()
        
        # 创建主窗口
        print("正在初始化主窗口...")
        window = UnifiedMonitoringApp()
        
        # 隐藏启动画面并显示主窗口
        splash.finish(window)
        window.show()
        
        print("应用启动成功！")
        print("\n功能说明：")
        print("- 🔥 热搜监控：实时监控热搜关键词数据")
        print("- 📈 股票L2监控：监控股票大单净额数据")
        print("- ⚡ 高性能设计：低延时、智能缓存、内存优化")
        print("- 🎯 智能更新：只更新活跃标签页，节省资源")
        print("\n使用提示：")
        print("- 使用标签页切换不同功能")
        print("- 支持自动刷新和手动刷新")
        print("- 支持数据导出功能")
        print("- 内存使用情况实时显示在状态栏")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有必要的文件都在同一目录下：")
        print("- unified_monitoring_app.py")
        print("- hot_search_widget.py")
        print("- stock_l2_widget.py")
        print("- data_manager.py")
        print("- performance_optimizer.py")
        
        # 如果有GUI环境，显示错误对话框
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "导入错误", 
                               f"无法导入必要模块：\n{str(e)}\n\n"
                               "请确保所有文件都在同一目录下。")
        except:
            pass
            
        sys.exit(1)
        
    except Exception as e:
        print(f"启动错误: {e}")
        print("详细错误信息：")
        traceback.print_exc()
        
        # 如果有GUI环境，显示错误对话框
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", 
                               f"应用启动失败：\n{str(e)}\n\n"
                               "请查看控制台获取详细错误信息。")
        except:
            pass
            
        sys.exit(1)

if __name__ == "__main__":
    main()
