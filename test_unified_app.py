#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一监控应用测试脚本
测试应用功能、性能和稳定性
"""

import sys
import time
import unittest
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt, QTimer

# 导入要测试的模块
try:
    from unified_monitoring_app import UnifiedMonitoringApp
    from hot_search_widget import HotSearchWidget, HotSearchAPI
    from stock_l2_widget import StockL2Widget, StockL2API
    from data_manager import (HotSearchDataManager, StockL2DataManager, 
                             DataCache, BaseDataManager)
    from performance_optimizer import (MemoryManager, UpdateScheduler, 
                                     PerformanceProfiler, UIOptimizer)
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestDataCache(unittest.TestCase):
    """测试数据缓存类"""
    
    def setUp(self):
        self.cache = DataCache(max_size=3, ttl_seconds=1)
        
    def test_set_and_get(self):
        """测试设置和获取数据"""
        self.cache.set("key1", "value1")
        self.assertEqual(self.cache.get("key1"), "value1")
        
    def test_expiration(self):
        """测试数据过期"""
        self.cache.set("key1", "value1")
        time.sleep(1.1)  # 等待过期
        self.assertIsNone(self.cache.get("key1"))
        
    def test_max_size(self):
        """测试最大容量限制"""
        for i in range(5):
            self.cache.set(f"key{i}", f"value{i}")
        
        # 应该只保留最后3个
        self.assertEqual(self.cache.size(), 3)
        self.assertIsNone(self.cache.get("key0"))
        self.assertIsNone(self.cache.get("key1"))
        
    def test_clear(self):
        """测试清空缓存"""
        self.cache.set("key1", "value1")
        self.cache.clear()
        self.assertEqual(self.cache.size(), 0)


class TestHotSearchDataManager(unittest.TestCase):
    """测试热搜数据管理器"""
    
    def setUp(self):
        self.manager = HotSearchDataManager()
        
    def tearDown(self):
        self.manager.cleanup()
        
    @patch('requests.Session.get')
    def test_fetch_data_success(self, mock_get):
        """测试成功获取数据"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.json.return_value = {
            'List': [
                {'KeyWord': '测试', 'Hot': 1000, 'Order': 1}
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        data = self.manager.fetch_data()
        self.assertIsNotNone(data)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['KeyWord'], '测试')
        
    @patch('requests.Session.get')
    def test_fetch_data_failure(self, mock_get):
        """测试获取数据失败"""
        mock_get.side_effect = Exception("网络错误")
        
        data = self.manager.fetch_data()
        self.assertIsNone(data)
        
    def test_process_data(self):
        """测试数据处理"""
        raw_data = [
            {'KeyWord': '测试1', 'Hot': 2000, 'Order': 1},
            {'KeyWord': '测试2', 'Hot': 1000, 'Order': 2}
        ]
        
        processed_data = self.manager.process_data(raw_data)
        
        # 应该按热度排序并添加排名
        self.assertEqual(processed_data[0]['Rank'], 1)
        self.assertEqual(processed_data[0]['KeyWord'], '测试1')
        self.assertEqual(processed_data[1]['Rank'], 2)
        
    def test_cache_key(self):
        """测试缓存键生成"""
        key = self.manager.get_cache_key()
        self.assertEqual(key, "hot_search_data")


class TestStockL2DataManager(unittest.TestCase):
    """测试股票L2数据管理器"""
    
    def setUp(self):
        self.manager = StockL2DataManager()
        
    def tearDown(self):
        self.manager.cleanup()
        
    @patch('requests.Session.get')
    def test_fetch_data_with_stock_code(self, mock_get):
        """测试使用股票代码获取数据"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'dadanjinge': [[1000, 50000], [1001, 60000]]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        data = self.manager.fetch_data(stock_code="002334")
        self.assertIsNotNone(data)
        self.assertIn('dadanjinge', data)
        
    def test_process_data(self):
        """测试数据处理"""
        raw_data = {
            'dadanjinge': [
                [1000, 50000],
                [1001, 60000],
                [1002, 40000]
            ]
        }
        
        processed_data = self.manager.process_data(raw_data)
        
        # 应该包含统计信息
        self.assertIn('statistics', processed_data)
        stats = processed_data['statistics']
        self.assertEqual(stats['current_value'], 4.0)  # 40000/10000
        self.assertEqual(stats['max_value'], 6.0)      # 60000/10000
        self.assertEqual(stats['min_value'], 4.0)      # 40000/10000
        
    def test_cache_key_with_stock_code(self):
        """测试带股票代码的缓存键"""
        key = self.manager.get_cache_key(stock_code="002334")
        self.assertEqual(key, "stock_l2_002334")


class TestPerformanceProfiler(unittest.TestCase):
    """测试性能分析器"""
    
    def setUp(self):
        self.profiler = PerformanceProfiler()
        
    def test_timing(self):
        """测试计时功能"""
        self.profiler.start_timing("test_operation")
        time.sleep(0.1)
        duration = self.profiler.end_timing("test_operation")
        
        self.assertIsNotNone(duration)
        self.assertGreaterEqual(duration, 0.1)
        
    def test_average_time(self):
        """测试平均时间计算"""
        # 执行多次操作
        for i in range(3):
            self.profiler.start_timing("test_op")
            time.sleep(0.05)
            self.profiler.end_timing("test_op")
            
        avg_time = self.profiler.get_average_time("test_op")
        self.assertIsNotNone(avg_time)
        self.assertGreaterEqual(avg_time, 0.05)
        
    def test_stats(self):
        """测试统计信息"""
        self.profiler.start_timing("test_op")
        time.sleep(0.05)
        self.profiler.end_timing("test_op")
        
        stats = self.profiler.get_stats("test_op")
        self.assertEqual(stats['count'], 1)
        self.assertGreaterEqual(stats['average'], 0.05)


class TestMemoryManager(unittest.TestCase):
    """测试内存管理器"""
    
    def setUp(self):
        self.memory_manager = MemoryManager(warning_threshold_mb=1.0)
        
    def tearDown(self):
        self.memory_manager.stop_monitoring()
        
    def test_memory_monitoring(self):
        """测试内存监控"""
        # 这个测试需要psutil
        try:
            import psutil
            memory_usage = self.memory_manager.get_memory_usage()
            self.assertIsNotNone(memory_usage)
            self.assertGreater(memory_usage, 0)
        except ImportError:
            self.skipTest("psutil not available")
            
    def test_garbage_collection(self):
        """测试垃圾回收"""
        # 创建一些对象
        test_objects = [[] for _ in range(1000)]
        del test_objects
        
        # 执行垃圾回收
        self.memory_manager.force_garbage_collection()
        # 这个测试主要确保方法不会抛出异常


class TestUIComponents(unittest.TestCase):
    """测试UI组件"""
    
    @classmethod
    def setUpClass(cls):
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
            
    def setUp(self):
        self.main_app = UnifiedMonitoringApp()
        
    def tearDown(self):
        self.main_app.close()
        
    def test_main_app_initialization(self):
        """测试主应用初始化"""
        self.assertIsNotNone(self.main_app)
        self.assertEqual(self.main_app.tab_widget.count(), 2)
        
    def test_tab_switching(self):
        """测试标签页切换"""
        # 切换到第一个标签页
        self.main_app.tab_widget.setCurrentIndex(0)
        QTest.qWait(100)  # 等待UI更新
        
        # 切换到第二个标签页
        self.main_app.tab_widget.setCurrentIndex(1)
        QTest.qWait(100)
        
        # 确保没有异常
        self.assertTrue(True)
        
    @patch('hot_search_widget.HotSearchAPI.fetch_data')
    def test_hot_search_widget(self, mock_fetch):
        """测试热搜组件"""
        mock_fetch.return_value = [
            {'KeyWord': '测试', 'Hot': 1000, 'Order': 1}
        ]
        
        widget = HotSearchWidget()
        widget.refresh_data()
        QTest.qWait(100)
        
        # 检查表格是否有数据
        self.assertGreater(widget.table.rowCount(), 0)
        
    def test_memory_warning_handling(self):
        """测试内存警告处理"""
        # 模拟内存警告
        self.main_app.on_memory_warning(250.0)
        
        # 确保状态更新
        self.assertIn("内存使用警告", self.main_app.status_label.text())


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @classmethod
    def setUpClass(cls):
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
            
    def test_full_application_workflow(self):
        """测试完整应用工作流程"""
        # 创建主应用
        main_app = UnifiedMonitoringApp()
        
        try:
            # 模拟用户操作
            main_app.show()
            QTest.qWait(100)
            
            # 切换标签页
            main_app.tab_widget.setCurrentIndex(0)
            QTest.qWait(100)
            
            main_app.tab_widget.setCurrentIndex(1)
            QTest.qWait(100)
            
            # 测试菜单操作
            main_app.refresh_current_tab()
            QTest.qWait(100)
            
            # 测试设置保存和加载
            main_app.save_settings()
            main_app.load_settings()
            
            self.assertTrue(True)  # 如果到这里没有异常，测试通过
            
        finally:
            main_app.close()


def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===")
    
    # 测试数据缓存性能
    cache = DataCache(max_size=1000, ttl_seconds=60)
    
    start_time = time.time()
    for i in range(1000):
        cache.set(f"key{i}", f"value{i}")
    set_time = time.time() - start_time
    
    start_time = time.time()
    for i in range(1000):
        cache.get(f"key{i}")
    get_time = time.time() - start_time
    
    print(f"缓存设置1000项耗时: {set_time:.4f}秒")
    print(f"缓存获取1000项耗时: {get_time:.4f}秒")
    
    # 测试数据管理器性能
    manager = HotSearchDataManager()
    
    with patch('requests.Session.get') as mock_get:
        mock_response = Mock()
        mock_response.json.return_value = {
            'List': [{'KeyWord': f'测试{i}', 'Hot': i*100, 'Order': i} 
                    for i in range(100)]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        start_time = time.time()
        for i in range(10):
            manager.update_data(force_refresh=True)
        process_time = time.time() - start_time
        
        print(f"数据管理器处理10次请求耗时: {process_time:.4f}秒")
    
    manager.cleanup()


def main():
    """主测试函数"""
    print("开始统一监控应用测试...")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
