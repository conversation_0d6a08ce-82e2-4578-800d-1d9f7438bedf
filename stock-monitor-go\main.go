package main

import (
	"log"
	"net/http"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 设置Gin为发布模式以减少资源占用
	gin.SetMode(gin.ReleaseMode)
	
	r := gin.New()
	
	// 使用轻量级中间件
	r.Use(gin.Recovery())
	
	// 配置CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type"}
	r.Use(cors.New(config))

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("templates/*")

	// 路由设置
	setupRoutes(r)

	// 获取端口
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 股票监控服务启动在端口 %s", port)
	log.Printf("📊 访问地址: http://localhost:%s", port)
	
	if err := r.Run(":" + port); err != nil {
		log.Fatal("服务启动失败:", err)
	}
}

func setupRoutes(r *gin.Engine) {
	// 主页
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "轻量级股票监控",
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 热搜数据
		api.GET("/hotsearch", getHotSearchData)
		
		// 股票L2数据
		api.GET("/stock/:code", getStockL2Data)
		
		// 健康检查
		api.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status": "ok",
				"service": "stock-monitor",
				"version": "1.0.0",
			})
		})
	}

	// WebSocket路由
	r.GET("/ws", handleWebSocket)
}
