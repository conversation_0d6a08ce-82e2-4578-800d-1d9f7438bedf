import tkinter as tk
from tkinter import ttk
import pygame
from datetime import datetime
from functools import partial
from tkinter.font import Font
from functools import partial, lru_cache
import time
import requests
import json
from datetime import datetime
import threading

pygame.mixer.init()
sound_file = "ding.mp3"
# API configurations
url = "https://apphwhq.longhuvip.com/w1/api/index.php"
querystring = {"a": "ZhiBoContent", "apiv": "w36", "c": "ConceptionPoint", "PhoneOSNew": "1",
               "DeviceID": "e283305172a73233", "VerSion": "********", "index": "0"}
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphwhq.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}


# Data fetching functions with caching
#@lru_cache(maxsize=1)
def fetch_theme_data():
    import requests
    url = "http://applhb.longhuvip.com/w1/api/index.php"
    querystring = {"a": "InfoList", "apiv": ["w36", "w36"], "c": ["Theme", "Theme"], "PhoneOSNew": ["1", "1"],
                   "UserID": ["2075379", "2075379"], "DeviceID": ["e283305172a73233", "e283305172a73233"],
                   "VerSion": ["********", "********"],
                   "Token": ["94db1204a4623a18cef3d341bc72e712", "94db1204a4623a18cef3d341bc72e712"]}
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Host": "applhb.longhuvip.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }
    response = requests.request("POST", url, headers=headers, params=querystring)
    response_data = json.loads(response.text)
    if response.status_code == 200:
        result = "\n".join(
            f"{item['Name']}: {datetime.fromtimestamp(int(item['CreateTime'])).strftime('%Y-%m-%d %H:%M:%S')} 涨停：{item['ZTNum']}  上涨：{item['UpNum']}"
            for item in response_data["New"]
        )
    else:
        result = None

    theme_data_label.config(text=result)

    return result


#@lru_cache(maxsize=1)
def fetch_emotion_data():
    import requests

    url = "https://apphq.longhuvip.com/w1/api/index.php"

    querystring = {"a": "ChangeStatistics", "apiv": "w31", "c": "HomeDingPan", "PhoneOSNew": "1", "UserID": "1410355",
                   "DeviceID": "e283305172a73233", "Token": "e52a0e9702dc3ef939fe77835d2ea694"}

    payload = "-----011000010111000001101001--\r\n\r\n"
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001"
    }

    response = requests.request("POST", url, data=payload, headers=headers, params=querystring)
    response_data = response.json()
    # 提取数据
    result = response_data if response.status_code == 200 else "请求失败"
    ztjs = response_data["info"][0]["ztjs"]
    strong = response_data["info"][0]["strong"]
    lbgd = response_data["info"][0]["lbgd"]
    day = response_data["info"][0]["Day"]
    df_num = response_data["info"][0]["df_num"]
    tip = "H(75) / L(25)"
    emotion_data_label.config(text=json.dumps(result, indent=2))  # 显示JSON数据
    emotion_data_label.config(text=f"情绪: {strong} 连板: {lbgd} 涨停: {ztjs} 大面: {df_num} \n {day} {tip}")
    if int(strong) > 75 or int(strong) < 25:
        # 加载音频文件
        sound = pygame.mixer.Sound(sound_file)
        # 播放声音
        sound.play()
    return result


def timestamp_to_time(timestamp):
    return datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')


def fetch_data(auto=True):
    """Fetch and update data, optionally indicating if called automatically."""
    global last_fetch_time
    try:
        response = requests.request("POST", url, headers=headers, params=querystring)
        data = json.loads(response.text)
        # Clear and refill Treeview with new data
        tree.delete(*tree.get_children())
        for item in data['List']:
            readable_time = timestamp_to_time(item.get('Time'))
            tree.insert('', tk.END, values=(readable_time, item.get('UserName'), item.get('Comment')))
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
    fetch_emotion_data()
    fetch_theme_data()

def make_request(url, querystring, headers):
        response = requests.post(url, headers=headers, params=querystring)
        print(response.text)
def permission_share_api(device_id, user_id, token):
        querystring = {
            "a": "PermissionShare",
            "apiv": "w36",
            "c": "Theme",
            "PhoneOSNew": "1",
            "UserID": user_id,
            "DeviceID": device_id,
            "VerSion": "********",
            "Token": token,
        }
        headers = {
            "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        }
        make_request("http://applhb.longhuvip.com/w1/api/index.php", querystring, headers)
def share_article(device_id, user_id, token):
    url = "https://applhb.longhuvip.com/w1/api/index.php"
    querystring = {
        "a": "ShareMsg",
        "apiv": "w36",
        "c": "Task",
        "PhoneOSNew": "1",
        "UserID": user_id,
        "DeviceID": device_id,
        "VerSion": "********",
        "Token": token,
    }
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    }
    make_request(url, querystring, headers)
def comment_adopted(device_id, user_id, token):
    url = "https://applhb.longhuvip.com/w1/api/index.php"
    querystring = {
        "a": "Comple",
        "apiv": "w36",
        "Type": "a5",
        "c": "Task",
        "TaskID": "5",
        "PhoneOSNew": "1",
        "UserID": user_id,
        "DeviceID": device_id,
        "Token": token,
    }
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001",
    }
    make_request(url, querystring, headers)
def share_in_circle_of_friends(device_id, user_id, token):
    url = "https://applhb.longhuvip.com/w1/api/index.php"
    querystring = {
        "a": "Comple",
        "apiv": "w36",
        "Type": "a11",
        "c": "Task",
        "TaskID": "5",
        "PhoneOSNew": "1",
        "UserID": user_id,
        "DeviceID": device_id,
        "Token": token,
    }
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001",
    }
    make_request(url, querystring, headers)

def sign_in(device_id, user_id, token):
    url = "https://applhb.longhuvip.com/w1/api/index.php"
    querystring = {
        "a": "Signin",
        "apiv": "w29",
        "c": "Task",
        "PhoneOSNew": "1",
        "UserID": user_id,
        "DeviceID": device_id,
        "Token": token,
    }
    payload = "-----011000010111000001101001--\r\n\r\n"
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001",
    }
    make_request(url, querystring, headers)

def invite(device_id, user_id, token):
    url = "https://applhb.longhuvip.com/w1/api/index.php"
    querystring = {"a": "Comple", "apiv": "w36", "Type": "a2", "c": "Task", "TaskID": "6", "PhoneOSNew": "1",
                   "UserID": user_id, "DeviceID": device_id, "Token": token}
    payload = "-----011000010111000001101001--\r\n\r\n"
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001",
    }
    make_request(url, querystring, headers)

def init():  #获取积分
    api_calls = [
        (sign_in, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (sign_in, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (sign_in, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (sign_in, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (sign_in, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b")),

        (permission_share_api, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (permission_share_api, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (permission_share_api, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (permission_share_api, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (permission_share_api, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b")),

        (share_article, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (share_article, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (share_article, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (share_article, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (share_article, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b")),

        (comment_adopted, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (comment_adopted, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (comment_adopted, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (comment_adopted, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (comment_adopted, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b")),

        (share_in_circle_of_friends, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (share_in_circle_of_friends, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (share_in_circle_of_friends, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (share_in_circle_of_friends, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (share_in_circle_of_friends, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b")),

        (invite, ("e283305172a73233", "2075379", "94db1204a4623a18cef3d341bc72e712")),
        (invite, ("e283305172a73233", "465276", "3c68c3cba897453f1d03cc1d254f704c")),
        (invite, ("e583a143c1a371b7", "1410355", "617e838dbafc9cc9b991d5b005b9adc6")),
        (invite, ("e754a188c2a351b3", "2575627", "536a95cf19f6c4e538a1437518c136e7")),
        (invite, ("37825ea487af865ee0349c458cb7925c", "736561", "ef87cb04e9633ae9a329a91080ac6d0b"))
    ]

    # 在后台线程中执行API调用
    for func, args in api_calls:
        thread = threading.Thread(target=func, args=args)
        time.sleep(1)
        thread.start()

def refresh_button_clicked():
    fetch_data(auto=True)
def do_one_iteration():
    fetch_data(auto=True)
    root.after(3000, do_one_iteration)



if __name__ == "__main__":
    # 创建主窗口，并设置起始大小
    root = tk.Tk()
    root.title("大盘")
    root.geometry("600x600")  # 设置窗口的宽和高

    emotion_data_label = tk.Label(root, text="", wraplength=400)  # 创建一个Label来显示情感数据
    emotion_data_label.pack(side=tk.BOTTOM, pady=10)  # 在刷新按钮下方添加
    theme_data_label = tk.Label(root, text="", wraplength=400, anchor=tk.CENTER)  # 创建一个Label来显示情感数据
    theme_data_label.pack(side=tk.BOTTOM, pady=10)  # 在刷新按钮下方添加

    # # 创建一个滚动条
    # scrollbar = ttk.Scrollbar(root, orient="vertical")
    # scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    tree = ttk.Treeview(root, columns=('Time', 'UserName', 'Comment'), show='headings')
    tree.heading('Time', text='时间')
    tree.heading('UserName', text='用户名')
    tree.heading('Comment', text='评论')
    tree.column('Time', width=130, stretch=tk.NO)
    tree.column('UserName', width=70, anchor='center', stretch=tk.NO)
    tree.column('Comment', width=5000, minwidth=300, anchor='w', stretch=tk.YES)

    # 绑定垂直和水平滚动条
    scrollbar_v = ttk.Scrollbar(root, orient="vertical")
    scrollbar_v.config(command=tree.yview)
    tree.config(yscrollcommand=scrollbar_v.set)
    scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)

    scrollbar_h = ttk.Scrollbar(root, orient="horizontal")
    scrollbar_h.config(command=tree.xview)
    tree.config(xscrollcommand=scrollbar_h.set)
    scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

    tree.pack(expand=True, fill='both')

    # Add manual refresh button
    refresh_button = ttk.Button(root, text="刷新", command=refresh_button_clicked)
    refresh_button.pack(side=tk.BOTTOM, pady=10)

    # # Initialize last fetch time
    # last_fetch_time = time.time()
    init()
    do_one_iteration()
    # Main event loop
    root.mainloop()
