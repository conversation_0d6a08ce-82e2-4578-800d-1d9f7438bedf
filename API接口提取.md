# 📡 开盘啦项目API接口提取

## 🎯 概述

从项目的Python文件中提取出所有使用的API接口，包括热搜监控、股票L2监控、大盘数据、公告等功能模块的API端点。

## 🔥 热搜监控API

### 接口信息
- **URL**: `https://apparticle.longhuvip.com/w1/api/index.php`
- **方法**: GET
- **功能**: 获取热搜关键词数据

### 请求参数
```json
{
    "a": "GetHotSearch",
    "apiv": "w31",
    "Type": "1",
    "c": "InteractData",
    "PhoneOSNew": "1",
    "DeviceID": "e283305172a73233",
    "VerSion": "*******"
}
```

### 请求头
```json
{
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}
```

### 响应格式
```json
{
    "List": [
        {
            "KeyWord": "关键词",
            "Hot": 54010,
            "Order": 4,
            "Sub": 0
        }
    ]
}
```

### 使用文件
- `HotSearchApp.py`
- `simple_solution/HotSearchApp.py`
- `hot_search_widget.py`
- `data_manager.py`

## 📈 股票L2监控API

### 接口信息
- **URL**: `https://apphq.longhuvip.com/w1/api/index.php`
- **方法**: GET
- **功能**: 获取股票L2大单净额数据

### 请求参数
```json
{
    "a": "GetStockDaDanTrendIncremental",
    "apiv": "w31",
    "c": "StockL2Data",
    "PhoneOSNew": "1",
    "UserID": "1410355",
    "DeviceID": "e283305172a73233",
    "VerSion": "*******",
    "Token": "e52a0e9702dc3ef939fe77835d2ea694",
    "StockID": "股票代码(如002334)"
}
```

### 请求头
```json
{
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}
```

### 使用文件
- `Da Dan L2.py`
- `Da Dan L2 v2.py`
- `simple_solution/Da Dan L2.py`
- `simple_solution/Da Dan L2 v2.py`
- `stock_l2_widget.py`
- `data_manager.py`

## 📊 大盘数据API

### 接口1: 直播内容API
- **URL**: `https://apphwhq.longhuvip.com/w1/api/index.php`
- **方法**: GET/POST
- **功能**: 获取概念点直播内容

### 请求参数
```json
{
    "a": "ZhiBoContent",
    "apiv": "w36",
    "c": "ConceptionPoint",
    "PhoneOSNew": "1",
    "DeviceID": "e283305172a73233",
    "VerSion": "********",
    "index": "0"
}
```

### 请求头
```json
{
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphwhq.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}
```

### 使用文件
- `dapan.py`
- `dapan2.py`
- `dapan_v2.py`

### 接口2: 情绪统计API
- **URL**: `https://apphq.longhuvip.com/w1/api/index.php`
- **方法**: POST
- **功能**: 获取市场情绪变化统计

### 请求参数
```json
{
    "a": "ChangeStatistics",
    "apiv": "w31",
    "c": "HomeDingPan",
    "PhoneOSNew": "1",
    "UserID": "1410355",
    "DeviceID": "e283305172a73233",
    "Token": "e52a0e9702dc3ef939fe77835d2ea694"
}
```

### 请求头
```json
{
    "User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}
```

### 使用文件
- `dapan.py`
- `dapan_v2.py`

## 📈 历史大盘数据API

### 接口信息
- **URL**: `https://apphis.longhuvip.com/w1/api/index.php`
- **方法**: POST
- **功能**: 获取历史大盘数据

### 请求参数
```json
{
    "a": "ChangeStatistics",
    "st": "100",
    "apiv": "w36",
    "c": "HisHomeDingPan",
    "PhoneOSNew": "1",
    "UserID": "2075379",
    "DeviceID": "e283305172a73233",
    "VerSion": "********",
    "Token": "94db1204a4623a18cef3d341bc72e712",
    "Index": "0"
}
```

### 请求头
```json
{
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphis.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}
```

### 使用文件
- `scqx.py`
- `scqx1.py`

## 📢 公告数据API

### 接口信息
- **URL**: `https://www.szse.cn/api/report/ShowReport/data`
- **方法**: GET
- **功能**: 获取深交所公告数据

### 请求参数
```json
{
    "SHOWTYPE": "JSON",
    "CATALOGID": "main_wxhj",
    "TABKEY": "tab3",
    "random": "0.8969034716429898"
}
```

### 请求头
```json
{
    "User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******"
}
```

### 使用文件
- `announcement.py`

## 🔧 API配置说明

### 通用设备信息
- **DeviceID**: `e283305172a73233` (固定设备标识)
- **PhoneOSNew**: `1` (手机操作系统标识)

### 用户认证信息
- **UserID**: `1410355` / `2075379` (用户ID，不同接口可能不同)
- **Token**: 
  - `e52a0e9702dc3ef939fe77835d2ea694`
  - `94db1204a4623a18cef3d341bc72e712`

### 版本信息
- **VerSion**: `*******` / `********` (应用版本)
- **apiv**: `w31` / `w36` (API版本)

## 📝 使用注意事项

1. **认证信息**: Token和UserID可能有时效性，需要定期更新
2. **设备标识**: DeviceID在所有接口中保持一致
3. **User-Agent**: 不同接口使用不同的User-Agent字符串
4. **请求频率**: 建议控制请求频率，避免被限制
5. **错误处理**: 所有API调用都包含异常处理机制
6. **缓存策略**: 统一应用中实现了缓存机制减少API调用

## 🔗 API域名总结

1. **apparticle.longhuvip.com** - 热搜数据
2. **apphq.longhuvip.com** - 股票L2数据、情绪统计
3. **apphwhq.longhuvip.com** - 直播内容、概念点
4. **apphis.longhuvip.com** - 历史大盘数据
5. **www.szse.cn** - 深交所公告数据

---

**提取时间**: 2025-01-08
**文档版本**: v1.0
**API总数**: 6个主要接口
