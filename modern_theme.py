#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化UI主题样式
提供美观的配色方案、字体、圆角、阴影等视觉效果
"""

from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtWidgets import QGraphicsDropShadowEffect
from PyQt5.QtGui import QColor, QFont, QPalette

class ModernTheme:
    """现代化主题配色方案"""
    
    # 主色调 - 深蓝色系
    PRIMARY = "#2196F3"
    PRIMARY_DARK = "#1976D2"
    PRIMARY_LIGHT = "#BBDEFB"
    
    # 辅助色 - 绿色系
    SECONDARY = "#4CAF50"
    SECONDARY_DARK = "#388E3C"
    SECONDARY_LIGHT = "#C8E6C9"
    
    # 警告色
    WARNING = "#FF9800"
    WARNING_DARK = "#F57C00"
    WARNING_LIGHT = "#FFE0B2"
    
    # 错误色
    ERROR = "#F44336"
    ERROR_DARK = "#D32F2F"
    ERROR_LIGHT = "#FFCDD2"
    
    # 成功色
    SUCCESS = "#4CAF50"
    SUCCESS_DARK = "#388E3C"
    SUCCESS_LIGHT = "#C8E6C9"
    
    # 中性色
    BACKGROUND = "#FAFAFA"
    SURFACE = "#FFFFFF"
    CARD = "#FFFFFF"
    DIVIDER = "#E0E0E0"
    
    # 文字色
    TEXT_PRIMARY = "#212121"
    TEXT_SECONDARY = "#757575"
    TEXT_HINT = "#BDBDBD"
    TEXT_ON_PRIMARY = "#FFFFFF"
    
    # 渐变色
    GRADIENT_START = "#2196F3"
    GRADIENT_END = "#21CBF3"

class ModernStyles:
    """现代化样式表"""
    
    @staticmethod
    def get_main_window_style():
        """主窗口样式"""
        return f"""
        QMainWindow {{
            background-color: {ModernTheme.BACKGROUND};
            color: {ModernTheme.TEXT_PRIMARY};
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }}
        
        QMenuBar {{
            background-color: {ModernTheme.SURFACE};
            border-bottom: 1px solid {ModernTheme.DIVIDER};
            padding: 4px;
            font-size: 14px;
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 2px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {ModernTheme.PRIMARY_LIGHT};
            color: {ModernTheme.PRIMARY_DARK};
        }}
        
        QMenu {{
            background-color: {ModernTheme.SURFACE};
            border: 1px solid {ModernTheme.DIVIDER};
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }}
        
        QMenu::item {{
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}
        
        QMenu::item:selected {{
            background-color: {ModernTheme.PRIMARY_LIGHT};
            color: {ModernTheme.PRIMARY_DARK};
        }}
        
        QStatusBar {{
            background-color: {ModernTheme.SURFACE};
            border-top: 1px solid {ModernTheme.DIVIDER};
            padding: 4px;
            font-size: 12px;
            color: {ModernTheme.TEXT_SECONDARY};
        }}
        """
    
    @staticmethod
    def get_tab_widget_style():
        """标签页样式"""
        return f"""
        QTabWidget::pane {{
            border: none;
            background-color: {ModernTheme.BACKGROUND};
            border-radius: 12px;
            margin-top: 8px;
        }}
        
        QTabWidget::tab-bar {{
            alignment: center;
        }}
        
        QTabBar::tab {{
            background-color: {ModernTheme.SURFACE};
            border: 2px solid {ModernTheme.DIVIDER};
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            border-bottom: none;
            font-size: 14px;
            font-weight: 500;
            color: {ModernTheme.TEXT_SECONDARY};
            min-width: 120px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {ModernTheme.PRIMARY};
            color: {ModernTheme.TEXT_ON_PRIMARY};
            border-color: {ModernTheme.PRIMARY};
            font-weight: 600;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {ModernTheme.PRIMARY_LIGHT};
            color: {ModernTheme.PRIMARY_DARK};
            border-color: {ModernTheme.PRIMARY_LIGHT};
        }}
        """
    
    @staticmethod
    def get_button_style():
        """按钮样式"""
        return f"""
        QPushButton {{
            background-color: {ModernTheme.PRIMARY};
            color: {ModernTheme.TEXT_ON_PRIMARY};
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {ModernTheme.PRIMARY_DARK};
        }}
        
        QPushButton:pressed {{
            background-color: {ModernTheme.PRIMARY_DARK};
            transform: translateY(1px);
        }}
        
        QPushButton:disabled {{
            background-color: {ModernTheme.TEXT_HINT};
            color: {ModernTheme.SURFACE};
        }}
        
        QPushButton.success {{
            background-color: {ModernTheme.SUCCESS};
        }}
        
        QPushButton.success:hover {{
            background-color: {ModernTheme.SUCCESS_DARK};
        }}
        
        QPushButton.warning {{
            background-color: {ModernTheme.WARNING};
        }}
        
        QPushButton.warning:hover {{
            background-color: {ModernTheme.WARNING_DARK};
        }}
        
        QPushButton.error {{
            background-color: {ModernTheme.ERROR};
        }}
        
        QPushButton.error:hover {{
            background-color: {ModernTheme.ERROR_DARK};
        }}
        """
    
    @staticmethod
    def get_card_style():
        """卡片样式"""
        return f"""
        QGroupBox {{
            background-color: {ModernTheme.CARD};
            border: 1px solid {ModernTheme.DIVIDER};
            border-radius: 12px;
            margin-top: 16px;
            padding-top: 16px;
            font-size: 16px;
            font-weight: 600;
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 8px 0 8px;
            background-color: {ModernTheme.CARD};
            color: {ModernTheme.PRIMARY};
        }}
        """
    
    @staticmethod
    def get_table_style():
        """表格样式"""
        return f"""
        QTableWidget {{
            background-color: {ModernTheme.SURFACE};
            border: 1px solid {ModernTheme.DIVIDER};
            border-radius: 12px;
            gridline-color: {ModernTheme.DIVIDER};
            selection-background-color: {ModernTheme.PRIMARY_LIGHT};
            selection-color: {ModernTheme.PRIMARY_DARK};
            font-size: 13px;
        }}
        
        QTableWidget::item {{
            padding: 12px 8px;
            border-bottom: 1px solid {ModernTheme.DIVIDER};
        }}
        
        QTableWidget::item:selected {{
            background-color: {ModernTheme.PRIMARY_LIGHT};
            color: {ModernTheme.PRIMARY_DARK};
        }}
        
        QHeaderView::section {{
            background-color: {ModernTheme.PRIMARY};
            color: {ModernTheme.TEXT_ON_PRIMARY};
            padding: 12px 8px;
            border: none;
            font-weight: 600;
            font-size: 14px;
        }}
        
        QHeaderView::section:first {{
            border-top-left-radius: 12px;
        }}
        
        QHeaderView::section:last {{
            border-top-right-radius: 12px;
        }}
        """
    
    @staticmethod
    def get_input_style():
        """输入框样式"""
        return f"""
        QLineEdit {{
            background-color: {ModernTheme.SURFACE};
            border: 2px solid {ModernTheme.DIVIDER};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QLineEdit:focus {{
            border-color: {ModernTheme.PRIMARY};
        }}
        
        QComboBox {{
            background-color: {ModernTheme.SURFACE};
            border: 2px solid {ModernTheme.DIVIDER};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
            min-width: 100px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernTheme.PRIMARY};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernTheme.TEXT_SECONDARY};
        }}
        
        QSpinBox {{
            background-color: {ModernTheme.SURFACE};
            border: 2px solid {ModernTheme.DIVIDER};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QSpinBox:focus {{
            border-color: {ModernTheme.PRIMARY};
        }}
        """
    
    @staticmethod
    def get_progress_style():
        """进度条样式"""
        return f"""
        QProgressBar {{
            background-color: {ModernTheme.DIVIDER};
            border: none;
            border-radius: 8px;
            height: 16px;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
        }}
        
        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernTheme.GRADIENT_START}, 
                stop:1 {ModernTheme.GRADIENT_END});
            border-radius: 8px;
        }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """复选框样式"""
        return f"""
        QCheckBox {{
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY};
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid {ModernTheme.DIVIDER};
            border-radius: 4px;
            background-color: {ModernTheme.SURFACE};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {ModernTheme.PRIMARY};
            border-color: {ModernTheme.PRIMARY};
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {ModernTheme.PRIMARY};
        }}
        """

class ModernEffects:
    """现代化视觉效果"""
    
    @staticmethod
    def add_shadow(widget, blur_radius=15, offset=(0, 2), color=QColor(0, 0, 0, 30)):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setOffset(offset[0], offset[1])
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)
    
    @staticmethod
    def create_fade_animation(widget, duration=300):
        """创建淡入淡出动画"""
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_slide_animation(widget, start_pos, end_pos, duration=300):
        """创建滑动动画"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(QRect(*start_pos))
        animation.setEndValue(QRect(*end_pos))
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation

def apply_modern_theme(app):
    """应用现代化主题到整个应用"""
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 组合所有样式
    style = (
        ModernStyles.get_main_window_style() +
        ModernStyles.get_tab_widget_style() +
        ModernStyles.get_button_style() +
        ModernStyles.get_card_style() +
        ModernStyles.get_table_style() +
        ModernStyles.get_input_style() +
        ModernStyles.get_progress_style() +
        ModernStyles.get_checkbox_style()
    )
    
    app.setStyleSheet(style)
    
    # 设置应用字体
    font = QFont("Segoe UI", 10)
    font.setHintingPreference(QFont.PreferFullHinting)
    app.setFont(font)
