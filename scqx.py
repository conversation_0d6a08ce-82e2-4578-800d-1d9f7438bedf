import matplotlib.pyplot as plt
import json

# JSON数据字符串
json_data = '''
{
    "info": [
        {
            "ztjs": "41",
            "strong": "49",
            "lbgd": "5",
            "Day": "2024-05-16",
            "df_num": "3"
        }
    ],
    "tip": "温馨提示：情绪指标过高（75），短期有释放亏钱效应的风险；情绪指标过低（25），短线有反弹回暖需求；提示仅供参考",
    "ttag": 0.0007949999999999902,
    "errcode": "0"
}
'''

# 解析JSON数据
data = json.loads(json_data)
info = data['info'][0]  # 获取info部分的第一个元素

# 提取数值型数据用于绘图
labels = ['ztjs', 'strong', 'lbgd', 'df_num']  # 标签
labels2 = ['涨停家数', '情绪指标', '连板高度', '亏钱效应']  # 更新标签

values = [int(info[label]) for label in labels]  # 对应的值

# 创建条形图
plt.figure(figsize=(10, 6))
plt.bar(labels2, values, color='skyblue')

# 添加标题和标签
plt.title('Info Data Visualization')
plt.xlabel('Indicators')
plt.ylabel('Values')

# 显示图形
plt.show()

# 提取数值型数据用于绘图
labels = ['涨停家数', '情绪指标', '连板高度', '亏钱效应']  # 更新标签


import requests

url = "https://apphis.longhuvip.com/w1/api/index.php"

querystring = {"a":"ChangeStatistics","st":"100","apiv":"w36","c":"HisHomeDingPan","PhoneOSNew":"1","UserID":"2075379","DeviceID":"e283305172a73233","VerSion":"********","Token":"94db1204a4623a18cef3d341bc72e712","Index":"0"}

headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphis.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}

response = requests.request("POST", url, headers=headers, params=querystring)

print(response.text)
import pandas as pd
import json
data = json.loads(response.text)
df = pd.DataFrame(data["info"])


