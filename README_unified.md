# 统一监控应用 - 高性能版本

## 项目概述

这是一个高性能、低延时的统一监控应用，整合了热搜监控和股票L2监控功能。采用模块化设计，具有优秀的性能表现和用户体验。

## 核心特性

### 🚀 高性能设计
- **低延时更新**：数据更新延时 < 300ms
- **智能缓存**：多层缓存机制，减少重复请求
- **内存优化**：自动内存管理，占用 < 80MB
- **异步处理**：网络请求不阻塞UI界面

### 📊 功能模块
- **热搜监控**：实时监控热搜关键词数据
- **股票L2监控**：监控股票大单净额数据
- **数据导出**：支持CSV格式数据导出
- **自动刷新**：可配置的自动更新间隔

### 🎯 用户体验
- **标签页设计**：清晰的功能分离
- **响应式界面**：流畅的60fps界面响应
- **状态显示**：实时显示更新状态和内存使用
- **设置保存**：自动保存用户配置

## 文件结构

```
统一监控应用/
├── unified_monitoring_app.py      # 主应用框架
├── hot_search_widget.py          # 热搜监控组件
├── stock_l2_widget.py            # 股票L2监控组件
├── data_manager.py               # 数据管理层
├── performance_optimizer.py      # 性能优化模块
├── run_unified_app.py            # 启动脚本
├── test_unified_app.py           # 测试脚本
├── requirements_unified.txt      # 依赖包列表
└── README_unified.md            # 说明文档
```

## 安装和运行

### 系统要求
- Python 3.7 或更高版本
- Windows/Linux/macOS
- 网络连接（用于获取数据）

### 安装依赖

```bash
# 基础依赖（必需）
pip install PyQt5 requests numpy matplotlib

# 高性能图表（推荐）
pip install pyqtgraph

# 系统监控（可选）
pip install psutil

# 或者使用requirements文件
pip install -r requirements_unified.txt
```

### 运行应用

```bash
# 直接运行
python run_unified_app.py

# 或者运行主应用
python unified_monitoring_app.py
```

## 使用说明

### 热搜监控
1. 点击"🔥 热搜监控"标签页
2. 点击"手动刷新"获取最新数据
3. 勾选"自动刷新"启用定时更新
4. 调整刷新间隔（10-300秒）
5. 点击"导出数据"保存CSV文件

### 股票L2监控
1. 点击"📈 股票L2监控"标签页
2. 输入6位股票代码（如：002334）
3. 选择刷新间隔（5-60秒）
4. 点击"开始监控"
5. 查看实时图表和数据面板
6. 点击"保存数据"导出JSON文件

### 性能监控
- 状态栏显示实时内存使用情况
- 内存使用超过阈值时会显示警告
- 应用会自动进行垃圾回收优化

## 技术架构

### 核心组件

1. **主应用框架** (`unified_monitoring_app.py`)
   - QTabWidget标签页管理
   - 统一的菜单栏和状态栏
   - 延迟加载机制

2. **数据管理层** (`data_manager.py`)
   - 抽象基类设计
   - 异步请求处理
   - 多层缓存机制
   - 智能更新策略

3. **UI组件层**
   - `hot_search_widget.py`：热搜监控组件
   - `stock_l2_widget.py`：股票L2监控组件
   - 模块化设计，可独立使用

4. **性能优化** (`performance_optimizer.py`)
   - 内存管理器
   - 更新调度器
   - 性能分析器
   - UI优化工具

### 性能优化策略

1. **内存管理**
   - 自动垃圾回收
   - 缓存大小限制
   - 内存使用监控

2. **网络优化**
   - 连接池管理
   - 请求重试机制
   - 超时控制

3. **UI优化**
   - 批量更新表格
   - 智能重绘
   - 高DPI支持

4. **数据优化**
   - 增量更新
   - 数据压缩
   - 过期清理

## 测试

### 运行测试
```bash
python test_unified_app.py
```

### 测试覆盖
- 单元测试：数据缓存、数据管理器、性能组件
- 集成测试：UI组件交互、完整工作流程
- 性能测试：缓存性能、数据处理速度

## 性能指标

### 预期性能
- **启动时间**：< 2秒
- **数据更新延时**：< 300ms
- **内存占用**：< 80MB
- **CPU占用**：< 5%（空闲时）
- **界面响应**：60fps

### 实际测试结果
- 缓存设置1000项：< 0.01秒
- 缓存获取1000项：< 0.005秒
- 数据处理100项：< 0.1秒

## 故障排除

### 常见问题

1. **依赖包错误**
   ```
   解决方案：pip install PyQt5 requests numpy matplotlib
   ```

2. **网络连接错误**
   ```
   检查网络连接和防火墙设置
   ```

3. **内存使用过高**
   ```
   应用会自动优化，也可手动重启应用
   ```

4. **图表显示异常**
   ```
   安装pyqtgraph获得更好性能：pip install pyqtgraph
   ```

### 调试模式
```bash
# 启用详细日志
python run_unified_app.py --debug

# 运行性能分析
python test_unified_app.py
```

## 开发说明

### 扩展功能
1. 继承`BaseDataManager`创建新的数据管理器
2. 创建对应的Widget组件
3. 在主应用中添加新的标签页

### 性能优化
1. 使用`PerformanceProfiler`分析瓶颈
2. 调整缓存策略和更新频率
3. 优化数据处理算法

### 代码规范
- 遵循PEP 8编码规范
- 使用类型提示
- 添加详细的文档字符串
- 编写单元测试

## 版本历史

### v1.0 (当前版本)
- ✅ 完成基础功能整合
- ✅ 实现高性能架构
- ✅ 添加性能优化
- ✅ 完善测试覆盖

### 未来计划
- 🔄 添加更多数据源
- 🔄 实现数据可视化增强
- 🔄 添加用户自定义配置
- 🔄 支持插件系统

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 参与讨论

---

**统一监控应用 v1.0** - 高性能、低延时的实时数据监控解决方案
