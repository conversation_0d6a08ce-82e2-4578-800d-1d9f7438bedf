version: '3.8'

services:
  stock-monitor:
    build: .
    container_name: stock-monitor
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.stock-monitor.rule=Host(`stock-monitor.localhost`)"
      - "traefik.http.services.stock-monitor.loadbalancer.server.port=8080"
    networks:
      - stock-monitor-network

networks:
  stock-monitor-network:
    driver: bridge
