#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化模块
提供各种性能优化策略和工具
"""

import gc
import time
import threading
from typing import Dict, List, Any, Optional
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from PyQt5.QtWidgets import QApplication


class MemoryManager(QObject):
    """内存管理器"""
    
    memory_warning = pyqtSignal(float)  # 内存警告信号
    
    def __init__(self, warning_threshold_mb: float = 200.0):
        super().__init__()
        self.warning_threshold = warning_threshold_mb * 1024 * 1024  # 转换为字节
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_memory)
        self.last_gc_time = time.time()
        self.gc_interval = 30  # 30秒执行一次垃圾回收
        
    def start_monitoring(self, interval_ms: int = 5000):
        """开始内存监控"""
        self.monitor_timer.start(interval_ms)
        
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitor_timer.stop()
        
    def check_memory(self):
        """检查内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_usage = process.memory_info().rss
            
            # 如果内存使用超过阈值，发出警告
            if memory_usage > self.warning_threshold:
                memory_mb = memory_usage / 1024 / 1024
                self.memory_warning.emit(memory_mb)
                
            # 定期执行垃圾回收
            current_time = time.time()
            if current_time - self.last_gc_time > self.gc_interval:
                self.force_garbage_collection()
                self.last_gc_time = current_time
                
        except ImportError:
            # 如果没有psutil，停止监控
            self.stop_monitoring()
            
    def force_garbage_collection(self):
        """强制执行垃圾回收"""
        collected = gc.collect()
        print(f"垃圾回收完成，回收了 {collected} 个对象")
        
    def get_memory_usage(self) -> Optional[float]:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return None


class UpdateScheduler(QObject):
    """智能更新调度器"""
    
    def __init__(self):
        super().__init__()
        self.active_components: Dict[str, Any] = {}
        self.update_priorities: Dict[str, int] = {}
        self.last_update_times: Dict[str, float] = {}
        self.min_update_intervals: Dict[str, float] = {}
        
    def register_component(self, name: str, component: Any, 
                          priority: int = 1, min_interval: float = 1.0):
        """注册需要更新的组件"""
        self.active_components[name] = component
        self.update_priorities[name] = priority
        self.min_update_intervals[name] = min_interval
        self.last_update_times[name] = 0
        
    def unregister_component(self, name: str):
        """注销组件"""
        if name in self.active_components:
            del self.active_components[name]
            del self.update_priorities[name]
            del self.last_update_times[name]
            del self.min_update_intervals[name]
            
    def should_update(self, name: str) -> bool:
        """判断组件是否应该更新"""
        if name not in self.active_components:
            return False
            
        current_time = time.time()
        last_update = self.last_update_times[name]
        min_interval = self.min_update_intervals[name]
        
        return current_time - last_update >= min_interval
        
    def mark_updated(self, name: str):
        """标记组件已更新"""
        self.last_update_times[name] = time.time()
        
    def get_update_order(self) -> List[str]:
        """获取更新顺序（按优先级排序）"""
        return sorted(self.active_components.keys(), 
                     key=lambda x: self.update_priorities[x], reverse=True)


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.timings: Dict[str, List[float]] = {}
        self.start_times: Dict[str, float] = {}
        
    def start_timing(self, operation: str):
        """开始计时"""
        self.start_times[operation] = time.time()
        
    def end_timing(self, operation: str):
        """结束计时"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            if operation not in self.timings:
                self.timings[operation] = []
            self.timings[operation].append(duration)
            del self.start_times[operation]
            return duration
        return None
        
    def get_average_time(self, operation: str) -> Optional[float]:
        """获取平均执行时间"""
        if operation in self.timings and self.timings[operation]:
            return sum(self.timings[operation]) / len(self.timings[operation])
        return None
        
    def get_stats(self, operation: str) -> Dict[str, float]:
        """获取操作统计信息"""
        if operation not in self.timings or not self.timings[operation]:
            return {}
            
        times = self.timings[operation]
        return {
            'count': len(times),
            'total': sum(times),
            'average': sum(times) / len(times),
            'min': min(times),
            'max': max(times)
        }
        
    def clear_stats(self, operation: str = None):
        """清除统计信息"""
        if operation:
            if operation in self.timings:
                self.timings[operation].clear()
        else:
            self.timings.clear()
            
    def print_report(self):
        """打印性能报告"""
        print("\n=== 性能分析报告 ===")
        for operation, times in self.timings.items():
            if times:
                stats = self.get_stats(operation)
                print(f"{operation}:")
                print(f"  调用次数: {stats['count']}")
                print(f"  平均时间: {stats['average']:.4f}s")
                print(f"  最小时间: {stats['min']:.4f}s")
                print(f"  最大时间: {stats['max']:.4f}s")
                print(f"  总时间: {stats['total']:.4f}s")
                print()


class UIOptimizer:
    """UI优化器"""
    
    @staticmethod
    def optimize_table_widget(table_widget):
        """优化表格控件性能"""
        # 禁用排序以提高性能
        table_widget.setSortingEnabled(False)
        
        # 设置统一行高
        table_widget.verticalHeader().setDefaultSectionSize(30)
        table_widget.verticalHeader().setSectionResizeMode(table_widget.verticalHeader().Fixed)
        
        # 优化选择模式
        table_widget.setSelectionBehavior(table_widget.SelectRows)
        
        # 禁用不必要的功能
        table_widget.setVerticalScrollMode(table_widget.ScrollPerPixel)
        table_widget.setHorizontalScrollMode(table_widget.ScrollPerPixel)
        
    @staticmethod
    def batch_update_table(table_widget, data: List[List[Any]]):
        """批量更新表格数据"""
        # 暂时禁用更新以提高性能
        table_widget.setUpdatesEnabled(False)
        
        try:
            table_widget.setRowCount(len(data))
            
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = table_widget.item(row_idx, col_idx)
                    if item is None:
                        from PyQt5.QtWidgets import QTableWidgetItem
                        item = QTableWidgetItem()
                        table_widget.setItem(row_idx, col_idx, item)
                    
                    item.setText(str(cell_data))
                    
        finally:
            # 重新启用更新
            table_widget.setUpdatesEnabled(True)
            
    @staticmethod
    def optimize_application():
        """优化应用程序设置"""
        app = QApplication.instance()
        if app:
            try:
                # 启用高DPI支持（PyQt5.6+）
                from PyQt5.QtCore import Qt
                if hasattr(Qt, 'AA_EnableHighDpiScaling'):
                    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
                if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
                    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

                # 优化绘制
                if hasattr(Qt, 'AA_CompressHighFrequencyEvents'):
                    app.setAttribute(Qt.AA_CompressHighFrequencyEvents, True)
                if hasattr(Qt, 'AA_CompressTabletEvents'):
                    app.setAttribute(Qt.AA_CompressTabletEvents, True)
            except AttributeError:
                # 忽略不支持的属性
                pass


class NetworkOptimizer:
    """网络优化器"""
    
    def __init__(self):
        self.connection_pool_size = 10
        self.timeout = 10
        self.retry_count = 3
        self.retry_delay = 1.0
        
    def create_optimized_session(self):
        """创建优化的请求会话"""
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.retry_count,
            backoff_factor=self.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 配置适配器
        adapter = HTTPAdapter(
            pool_connections=self.connection_pool_size,
            pool_maxsize=self.connection_pool_size,
            max_retries=retry_strategy
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认超时
        session.timeout = self.timeout
        
        return session


# 全局性能优化实例
memory_manager = MemoryManager()
update_scheduler = UpdateScheduler()
profiler = PerformanceProfiler()
ui_optimizer = UIOptimizer()
network_optimizer = NetworkOptimizer()


def initialize_performance_optimization():
    """初始化性能优化"""
    # 优化应用程序
    ui_optimizer.optimize_application()
    
    # 启动内存监控
    memory_manager.start_monitoring()
    
    print("性能优化已初始化")


def cleanup_performance_optimization():
    """清理性能优化"""
    memory_manager.stop_monitoring()
    profiler.print_report()
    print("性能优化已清理")
