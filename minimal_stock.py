
import sys
import os
import json
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    import requests
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt5和requests")
    input("按回车键退出...")
    sys.exit(1)

class StockL2App(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("股票L2监控")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建输入区域
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("股票代码:"))
        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("请输入6位股票代码，如：002334")
        input_layout.addWidget(self.stock_input)
        
        input_layout.addWidget(QLabel("刷新间隔:"))
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["5秒", "10秒", "30秒", "1分钟", "5分钟"])
        self.interval_combo.setCurrentText("10秒")
        input_layout.addWidget(self.interval_combo)
        
        # 创建控制按钮
        self.start_btn = QPushButton("▶ 开始监控")
        self.stop_btn = QPushButton("■ 停止")
        self.save_btn = QPushButton("💾 保存数据")
        
        input_layout.addWidget(self.start_btn)
        input_layout.addWidget(self.stop_btn)
        input_layout.addWidget(self.save_btn)
        
        # 创建数据显示区域
        self.data_text = QTextEdit()
        self.data_text.setReadOnly(True)
        
        layout.addLayout(input_layout)
        layout.addWidget(QLabel("L2大单数据:"))
        layout.addWidget(self.data_text)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_monitoring)
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.save_btn.clicked.connect(self.save_data)
        
        # 监控定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.fetch_data)
        self.monitoring = False
        
        self.stock_code = ""
        self.data_history = []
    
    def start_monitoring(self):
        """开始监控"""
        self.stock_code = self.stock_input.text().strip()
        if not self.stock_code:
            QMessageBox.warning(self, "警告", "请输入股票代码")
            return
        
        if len(self.stock_code) != 6 or not self.stock_code.isdigit():
            QMessageBox.warning(self, "警告", "请输入正确的6位股票代码")
            return
        
        # 获取刷新间隔
        interval_text = self.interval_combo.currentText()
        interval_map = {"5秒": 5000, "10秒": 10000, "30秒": 30000, "1分钟": 60000, "5分钟": 300000}
        interval = interval_map.get(interval_text, 10000)
        
        self.timer.start(interval)
        self.monitoring = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        self.data_text.append(f"开始监控股票 {self.stock_code}，刷新间隔: {interval_text}")
        self.fetch_data()  # 立即获取一次数据
    
    def stop_monitoring(self):
        """停止监控"""
        self.timer.stop()
        self.monitoring = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.data_text.append("监控已停止")
    
    def fetch_data(self):
        """获取数据"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # 模拟L2数据（实际应用中这里会调用API）
            import random
            sample_data = {
                "time": current_time,
                "stock_code": self.stock_code,
                "big_order_net": random.randint(-1000000, 1000000),
                "buy_volume": random.randint(100000, 500000),
                "sell_volume": random.randint(100000, 500000),
                "price": round(random.uniform(10.0, 50.0), 2)
            }
            
            self.data_history.append(sample_data)
            
            # 显示数据
            display_text = f"[{current_time}] {self.stock_code} - "
            display_text += f"大单净额: {sample_data['big_order_net']:,} "
            display_text += f"买入: {sample_data['buy_volume']:,} "
            display_text += f"卖出: {sample_data['sell_volume']:,} "
            display_text += f"价格: {sample_data['price']}"
            
            self.data_text.append(display_text)
            
            # 自动滚动到底部
            scrollbar = self.data_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            self.data_text.append(f"获取数据失败: {str(e)}")
    
    def save_data(self):
        """保存数据"""
        try:
            if not self.data_history:
                QMessageBox.warning(self, "警告", "没有数据可保存")
                return
            
            filename = f"L2_Data_{self.stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.data_history, f, ensure_ascii=False, indent=4)
            
            QMessageBox.information(self, "保存成功", f"数据已保存到 {filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "保存错误", str(e))

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = StockL2App()
    window.show()
    sys.exit(app.exec_())
