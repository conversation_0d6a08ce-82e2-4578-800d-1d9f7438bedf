import sys
import requests
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QHBoxLayout, QLabel, QHeaderView,
                            QMessageBox)
from PyQt5.QtCore import QTimer, Qt
from datetime import datetime

class HotSearchAPI:
    def __init__(self):
        self.base_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.params = {
            "a": "GetHotSearch",
            "apiv": "w31",
            "Type": "1",
            "c": "InteractData",
            "PhoneOSNew": "1",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******"
        }

    def fetch_data(self):
        try:
            response = requests.get(self.base_url, params=self.params, headers=self.headers)
            response.raise_for_status()
            return response.json().get('List', [])
        except Exception as e:
            print(f"API请求错误: {e}")
            return []

class HotSearchApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.api = HotSearchAPI()
        self.timer = QTimer()
        self.init_ui()
        self.load_initial_data()

    def init_ui(self):
        self.setWindowTitle("热搜数据监控")
        self.setGeometry(100, 100, 800, 600)

        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)

        # 控制面板
        control_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("手动刷新")
        self.refresh_btn.clicked.connect(self.load_data)
        self.auto_refresh = QPushButton("开启自动刷新")
        self.auto_refresh.setCheckable(True)
        self.auto_refresh.clicked.connect(self.toggle_auto_refresh)
        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.export_data)

        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(self.auto_refresh)
        control_layout.addWidget(self.export_btn)
        control_layout.setSpacing(15)
        control_layout.setContentsMargins(10, 10, 10, 10)

        for button in [self.refresh_btn, self.auto_refresh, self.export_btn]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    min-width: 100px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #006CBB;
                }
                QPushButton:checked {
                    background-color: #005999;
                }
            """)

        # 数据表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["关键词", "热度值", "排位变化", "实时排名"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)
        self.table.setSortingEnabled(True)
        self.table.horizontalHeader().setSortIndicator(1, Qt.DescendingOrder)
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #3A3A3A;
                alternate-background-color: #454545;
                color: #FFFFFF;
                gridline-color: #505050;
                border: 1px solid #606060;
                border-radius: 8px;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 10px;
                text-align: center;
            }
            QHeaderView::section {
                background-color: #404040;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
            QHeaderView::down-arrow, QHeaderView::up-arrow {
                image: none;
            }
        """)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_label = QLabel("就绪")
        self.status_bar.addPermanentWidget(self.status_label)

        layout.addLayout(control_layout)
        layout.addWidget(self.table)

    def load_initial_data(self):
        self.timer.timeout.connect(self.load_data)
        self.load_data()

    def load_data(self):
        data = self.api.fetch_data()
        if data:
            self.update_table(data)
            self.status_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")

    def update_table(self, data):
        self.table.setRowCount(len(data))
        sorted_data = sorted(data, key=lambda x: x.get('Hot', 0), reverse=True)
        rank_dict = {item['KeyWord']: idx + 1 for idx, item in enumerate(sorted_data)}
        for row_idx, item in enumerate(data):
            rank = rank_dict.get(item.get('KeyWord', ''), 0)
            self.table.setItem(row_idx, 0, QTableWidgetItem(item.get('KeyWord', '')))
            # 设置数值类型数据用于正确排序
            hot_item = QTableWidgetItem()
            hot_item.setData(Qt.DisplayRole, int(item.get('Hot', 0)))
            self.table.setItem(row_idx, 1, hot_item)
            
            order_item = QTableWidgetItem()
            order_item.setData(Qt.DisplayRole, int(item.get('Order', 0)))
            self.table.setItem(row_idx, 2, order_item)
            
            rank_item = QTableWidgetItem()
            rank_item.setData(Qt.DisplayRole, rank)
            self.table.setItem(row_idx, 3, rank_item)
            for col in range(4):
                item = self.table.item(row_idx, col)
                if item is not None:
                    item.setTextAlignment(Qt.AlignCenter)
            

    def toggle_auto_refresh(self):
        if self.auto_refresh.isChecked():
            self.timer.start(10000)  # 10秒刷新
            self.auto_refresh.setText("停止自动刷新")
        else:
            self.timer.stop()
            self.auto_refresh.setText("开启自动刷新")

    def export_data(self):
        try:
            import os
            # 使用当前目录而不是硬编码路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            filename = os.path.join(current_dir, f"hotsearch_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("关键词,热度值,排位变化,实时排名\n")
                for row in range(self.table.rowCount()):
                    items = [self.table.item(row, col).text() for col in range(4)]
                    f.write(','.join(items) + '\n')
            QMessageBox.information(self, "导出成功", f"数据已保存到 {filename}")
        except Exception as e:
            QMessageBox.critical(self, "导出错误", str(e))

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    app.setPalette(QApplication.style().standardPalette())
    window = HotSearchApp()
    window.show()
    sys.exit(app.exec_())