@echo off
chcp 65001 >nul
echo ===============================================
echo    股票监控应用 - 安全版启动器
echo ===============================================
echo.

echo 选择要启动的应用:
echo 1. 热搜监控 (安全版)
echo 2. 股票L2监控 (安全版)
echo 3. 退出
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo 启动热搜监控安全版...
    if exist "dist\热搜监控_安全版\热搜监控_安全版.exe" (
        start "" "dist\热搜监控_安全版\热搜监控_安全版.exe"
    ) else (
        echo 错误: 找不到热搜监控安全版
        pause
    )
) else if "%choice%"=="2" (
    echo 启动股票L2监控安全版...
    if exist "dist\股票L2监控_安全版\股票L2监控_安全版.exe" (
        start "" "dist\股票L2监控_安全版\股票L2监控_安全版.exe"
    ) else (
        echo 错误: 找不到股票L2监控安全版
        pause
    )
) else if "%choice%"=="3" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择
    pause
    goto :eof
)
