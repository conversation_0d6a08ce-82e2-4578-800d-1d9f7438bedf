# 股票监控应用 - 安全版

## 🛡️ 安全版特点

这是专门为解决DLL依赖问题而创建的安全版本：

### ✅ 解决的问题
- 修复了 `ImportError: DLL load failed while importing pyexpat` 错误
- 移除了problematic的pkg_resources依赖
- 使用目录模式避免DLL冲突
- 最小化依赖，只保留核心功能

### 📁 文件结构
```
dist/
├── 热搜监控_安全版/
│   ├── 热搜监控_安全版.exe
│   └── [依赖文件...]
└── 股票L2监控_安全版/
    ├── 股票L2监控_安全版.exe
    └── [依赖文件...]
```

## 🚀 使用方法

### 方式1：使用启动器（推荐）
双击 `启动安全版.bat` 选择要运行的应用

### 方式2：直接运行
1. 进入对应的应用目录
2. 双击 `.exe` 文件运行

## 📋 功能说明

### 热搜监控_安全版
- ✅ 实时数据显示（示例数据）
- ✅ 自动刷新功能
- ✅ 数据导出功能
- ✅ 稳定运行，无DLL问题

### 股票L2监控_安全版  
- ✅ 股票代码输入
- ✅ 多种刷新间隔
- ✅ 实时数据监控（示例数据）
- ✅ 数据保存功能
- ✅ 稳定运行，无DLL问题

## ⚠️ 注意事项

1. **示例数据**: 安全版使用示例数据，如需真实数据请参考原版代码
2. **目录完整性**: 请保持目录结构完整，不要移动exe文件
3. **首次运行**: 可能需要允许通过防火墙

## 🔧 如需真实数据

如果需要连接真实API获取数据，请：
1. 修改 `minimal_hotsearch.py` 和 `minimal_stock.py` 中的数据获取部分
2. 添加真实的API调用代码
3. 重新构建应用

## 📞 技术支持

如果安全版仍有问题，请：
1. 检查Windows版本兼容性
2. 尝试以管理员身份运行
3. 检查杀毒软件设置

---

**版本**: 安全版 v1.0  
**特点**: 无DLL依赖问题，稳定运行
