@echo off
chcp 65001 >nul
echo ===============================================
echo    股票监控应用 - 测试运行脚本
echo ===============================================
echo.

echo 正在测试可执行文件...
echo.

echo 1. 测试热搜监控应用
echo 文件位置: portable_version\热搜监控.exe
if exist "portable_version\热搜监控.exe" (
    echo ✅ 热搜监控.exe 存在
    for %%A in ("portable_version\热搜监控.exe") do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 热搜监控.exe 不存在
)

echo.
echo 2. 测试股票L2监控应用
echo 文件位置: portable_version\股票L2监控.exe
if exist "portable_version\股票L2监控.exe" (
    echo ✅ 股票L2监控.exe 存在
    for %%A in ("portable_version\股票L2监控.exe") do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 股票L2监控.exe 不存在
)

echo.
echo 3. 检查说明文件
if exist "portable_version\README.md" (
    echo ✅ README.md 存在
)
if exist "portable_version\运行说明.txt" (
    echo ✅ 运行说明.txt 存在
)

echo.
echo ===============================================
echo 测试完成！您可以：
echo 1. 进入 portable_version 目录
echo 2. 双击对应的 .exe 文件运行
echo 3. 或者使用以下命令启动：
echo    start portable_version\热搜监控.exe
echo    start portable_version\股票L2监控.exe
echo ===============================================
echo.
pause
