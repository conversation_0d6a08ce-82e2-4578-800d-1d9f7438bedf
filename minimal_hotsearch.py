
import sys
import os
import json
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    import requests
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt5和requests")
    input("按回车键退出...")
    sys.exit(1)

class HotSearchApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("热搜监控")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建按钮
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("手动刷新")
        self.auto_btn = QPushButton("开启自动刷新")
        self.export_btn = QPushButton("导出数据")
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.auto_btn)
        button_layout.addWidget(self.export_btn)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["关键词", "热度值", "排位变化", "实时排名"])
        
        layout.addLayout(button_layout)
        layout.addWidget(self.table)
        
        # 连接信号
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.export_btn.clicked.connect(self.export_data)
        
        # 自动刷新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)
        self.auto_refresh = False
        self.auto_btn.clicked.connect(self.toggle_auto_refresh)
        
        # 初始化数据
        self.refresh_data()
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 模拟数据（实际应用中这里会调用API）
            sample_data = [
                ["示例关键词1", "100000", "+5", "1"],
                ["示例关键词2", "95000", "-2", "2"],
                ["示例关键词3", "90000", "+1", "3"],
                ["示例关键词4", "85000", "0", "4"],
                ["示例关键词5", "80000", "+3", "5"],
            ]
            
            self.table.setRowCount(len(sample_data))
            for row, data in enumerate(sample_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    self.table.setItem(row, col, item)
            
            print(f"数据刷新完成: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新数据失败: {str(e)}")
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if self.auto_refresh:
            self.timer.stop()
            self.auto_btn.setText("开启自动刷新")
            self.auto_refresh = False
        else:
            self.timer.start(10000)  # 10秒
            self.auto_btn.setText("关闭自动刷新")
            self.auto_refresh = True
    
    def export_data(self):
        """导出数据"""
        try:
            filename = f"hotsearch_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("关键词,热度值,排位变化,实时排名\n")
                for row in range(self.table.rowCount()):
                    items = [self.table.item(row, col).text() for col in range(4)]
                    f.write(','.join(items) + '\n')
            QMessageBox.information(self, "导出成功", f"数据已保存到 {filename}")
        except Exception as e:
            QMessageBox.critical(self, "导出错误", str(e))

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = HotSearchApp()
    window.show()
    sys.exit(app.exec_())
