
import sys
from cx_Freeze import setup, Executable

# 依赖包
packages = ["PyQt5", "requests", "json", "datetime", "os", "sys"]

# 包含的文件
include_files = []

# 排除的模块
excludes = ["matplotlib", "numpy", "pandas", "IPython", "sphinx", "tkinter"]

# 构建选项
build_exe_options = {
    "packages": packages,
    "excludes": excludes,
    "include_files": include_files,
    "optimize": 2,
}

# 可执行文件配置
executables = [
    Executable(
        "HotSearchApp.py",
        base="Win32GUI" if sys.platform == "win32" else None,
        target_name="热搜监控_CX.exe",
        icon=None
    ),
    Executable(
        "Da Dan L2 v2.py", 
        base="Win32GUI" if sys.platform == "win32" else None,
        target_name="股票L2监控_CX.exe",
        icon=None
    )
]

setup(
    name="股票监控应用",
    version="1.0",
    description="股票监控应用 - cx_Freeze版本",
    options={"build_exe": build_exe_options},
    executables=executables,
)
