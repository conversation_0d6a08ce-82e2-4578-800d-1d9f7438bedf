# https://apphq.longhuvip.com/w1/api/index.php?a=GetStockDaDanTrendIncremental&apiv=w31&c=StockL2Data&StockID=002334&PhoneOSNew=1&UserID=1410355&DeviceID=e283305172a73233&VerSion=*******&Token=e52a0e9702dc3ef939fe77835d2ea694&

import requests
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import numpy as np
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QLineEdit, QWidget, QComboBox, QMessageBox, QGroupBox
from PyQt5.QtCore import QTimer, Qt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure  # Add this import
from matplotlib.widgets import Cursor
from PyQt5.QtGui import QFont, QIcon

class StockL2DataQuery:
    def __init__(self):
        self.base_url = "https://apphq.longhuvip.com/w1/api/index.php"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        # 这些参数需要根据实际情况修改
        self.default_params = {
            "a": "GetStockDaDanTrendIncremental",
            "apiv": "w31",
            "c": "StockL2Data",
            "PhoneOSNew": "1",
            "UserID": "1410355",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******",
            "Token": "e52a0e9702dc3ef939fe77835d2ea694"
        }
    
    def query_stock_l2_data(self, stock_code):
        """
        查询特定股票的L2大单数据
        
        Args:
            stock_code (str): 股票代码，如 "002334"
            
        Returns:
            dict: 包含L2大单数据的字典
        """
        params = self.default_params.copy()
        params["StockID"] = stock_code
        
        try:
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            return data
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
    
    def save_to_file(self, data, stock_code):
        """
        将数据保存到文件

        Args:
            data (dict): L2数据
            stock_code (str): 股票代码
        """
        import os
        today = datetime.now().strftime("%Y%m%d")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = os.path.join(current_dir, f"L2_Data_{stock_code}_{today}.json")

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        print(f"数据已保存到: {filename}")
        return filename
    def get_chart_data(self, data):
        """
        获取用于绘图的数据
        
        Args:
            data (dict): L2数据
            
        Returns:
            tuple: (times, values, date_str)
        """
        if not data or "dadanjinge" not in data:
            return None, None, None
        
        dadan_data = data["dadanjinge"]
        times = [item[0] for item in dadan_data]
        values = [item[1] / 10000 for item in dadan_data]  # 转换为万元单位
        date_str = data.get("day", datetime.now().strftime("%Y%m%d"))
        
        return times, values, date_str
    def analyze_dadan_trend(self, data):
        """
        分析大单净额趋势
        
        Args:
            data (dict): L2数据
            
        Returns:
            dict: 分析结果
        """
        if not data or "dadanjinge" not in data:
            return {"error": "无效数据"}
        
        dadan_data = data["dadanjinge"]
        
        # 计算最大值、最小值和当前值
        current_value = dadan_data[-1][1] if dadan_data else 0
        max_value = data.get("max", 0)
        min_value = data.get("min", 0)
        
        # 计算上午和下午的趋势
        morning_data = [item for item in dadan_data if item[0] < "11:30"]
        afternoon_data = [item for item in dadan_data if item[0] >= "13:00"]
        
        morning_trend = morning_data[-1][1] - morning_data[0][1] if morning_data else 0
        afternoon_trend = afternoon_data[-1][1] - afternoon_data[0][1] if afternoon_data else 0
        
        return {
            "stock_code": data.get("code", ""),
            "date": data.get("day", ""),
            "current_value": current_value,
            "max_value": max_value,
            "min_value": min_value,
            "morning_trend": morning_trend,
            "afternoon_trend": afternoon_trend,
            "total_trend": current_value - dadan_data[0][1] if dadan_data else 0
        }
    def visualize_dadan_data(self, data, stock_code):
        """
        可视化大单净额数据
        
        Args:
            data (dict): L2数据
            stock_code (str): 股票代码
        """
        if not data or "dadanjinge" not in data:
            print("无效数据，无法可视化")
            return
        
        # 设置中文字体
        try:
            font = FontProperties(family='SimHei', size=12)
        except:
            font = FontProperties(family='sans-serif', size=12)
        
        dadan_data = data["dadanjinge"]
        times = [item[0] for item in dadan_data]
        values = [item[1] / 10000 for item in dadan_data]  # 转换为万元单位
        
        # 创建图形和子图
        fig, ax = plt.subplots(figsize=(15, 8))
        
        # 绘制大单净额曲线
        ax.plot(times, values, 'b-', linewidth=2)
        
        # 填充颜色区域（正值为红色，负值为绿色）
        ax.fill_between(times, values, 0, where=(np.array(values) > 0), 
                        color='red', alpha=0.3, interpolate=True)
        ax.fill_between(times, values, 0, where=(np.array(values) <= 0), 
                        color='green', alpha=0.3, interpolate=True)
        
        # 添加零线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 添加标题和标签
        date_str = data.get("day", datetime.now().strftime("%Y%m%d"))
        plt.title(f"股票 {stock_code} 大单净额走势图 ({date_str})", fontproperties=font, fontsize=16)
        plt.xlabel("时间", fontproperties=font, fontsize=12)
        plt.ylabel("大单净额(万元)", fontproperties=font, fontsize=12)
        
        # 设置x轴刻度
        plt.xticks(rotation=45)
        
        # 每隔10个点显示一个刻度
        ax.set_xticks([times[i] for i in range(0, len(times), 10)])
        
        # 添加网格
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 添加最大值和最小值标记
        max_idx = values.index(max(values))
        min_idx = values.index(min(values))
        
        plt.annotate(f"最大值: {values[max_idx]:.2f}万",
                    xy=(times[max_idx], values[max_idx]),
                    xytext=(0, 20), textcoords='offset points',
                    arrowprops=dict(arrowstyle="->"),
                    fontproperties=font)
        
        plt.annotate(f"最小值: {values[min_idx]:.2f}万",
                    xy=(times[min_idx], values[min_idx]),
                    xytext=(0, -20), textcoords='offset points',
                    arrowprops=dict(arrowstyle="->"),
                    fontproperties=font)
        
        # 添加当前值标记
        current_value = values[-1]
        plt.annotate(f"当前值: {current_value:.2f}万",
                    xy=(times[-1], current_value),
                    xytext=(-50, 30), textcoords='offset points',
                    arrowprops=dict(arrowstyle="->"),
                    fontproperties=font)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        import os
        today = datetime.now().strftime("%Y%m%d")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        img_path = os.path.join(current_dir, f"L2_Chart_{stock_code}_{today}.png")
        plt.savefig(img_path, dpi=300)
        print(f"图表已保存到: {img_path}")
        
        # 显示图表
        plt.show()

class EnhancedMatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=12, height=6, dpi=100):
        # Set default font to support Chinese characters
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Zen Hei']
        plt.rcParams['axes.unicode_minus'] = False  # Ensure minus signs are displayed correctly
        
        self.fig = Figure(figsize=(width, height), dpi=dpi, facecolor='#F5F5F5')
        super().__init__(self.fig)
        self.ax = self.fig.add_subplot(111, facecolor='#FFFFFF')
        self.setParent(parent)
        self.setMinimumSize(800, 400)
        self.fig.subplots_adjust(left=0.08, right=0.95, top=0.92, bottom=0.15)
        
        # Interactive elements
        self.annotation = self.ax.annotate("", xy=(0,0), xytext=(20,20),
                                          textcoords="offset points",
                                          bbox=dict(boxstyle="round", fc="#F5F5F5", ec="#808080"),
                                          arrowprops=dict(arrowstyle="->"))
        self.annotation.set_visible(False)
        self.cursor = Cursor(self.ax, useblit=True, color='#606060', linestyle='--')
        
        # Connect events
        self.mpl_connect("motion_notify_event", self.on_hover)
        self.mpl_connect("figure_enter_event", lambda event: self.setFocus())

    def on_hover(self, event):
        if event.inaxes == self.ax:
            x = event.xdata
            y = event.ydata
            if x is not None and y is not None:
                # Convert numerical x back to time string
                line = self.ax.lines[0]
                xdata_num = line.get_xdata()  # Numerical x values
                xdata_str = self.original_times  # Store original time strings
                ydata = line.get_ydata()
                
                # Find nearest index using numerical values
                idx = np.abs(xdata_num - x).argmin()
                
                # Get corresponding string time
                time_str = xdata_str[idx]
                value = ydata[idx]
                
                self.annotation.xy = (xdata_num[idx], value)
                self.annotation.set_text(f"时间: {time_str}\n金额: {value:.2f}万")
                self.annotation.set_visible(True)
                self.fig.canvas.draw_idle()
        else:
            self.annotation.set_visible(False)
            self.fig.canvas.draw_idle()

class StockL2App(QMainWindow):
    def __init__(self):
        super().__init__()
        self.query = StockL2DataQuery()
        self.stock_code = ""
        self.data = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("📈 股票L2大单分析 - 专业版")
        self.setGeometry(100, 100, 1280, 800)
        
        # Modern font setup
        font = QFont()
        font.setFamily("Segoe UI")
        self.setFont(font)
        
        # Main widget and layout
        main_widget = QWidget()
        main_widget.setStyleSheet("background-color: #F5F5F5;")
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # Control Panel
        control_group = QGroupBox("控制设置")
        control_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 20px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                color: #404040;
                font-weight: bold;
            }
        """)
        
        # 股票代码输入
        self.code_label = QLabel("股票代码:")
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("例如: 002334")
        
        # 刷新间隔选择
        self.interval_label = QLabel("刷新间隔:")
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["5秒", "10秒", "30秒", "60秒", "300秒"])
        self.interval_combo.setCurrentIndex(1)  # 默认10秒
        
        # 查询按钮
        self.query_btn = QPushButton("开始查询")
        self.query_btn.clicked.connect(self.start_query)
        
        # 停止按钮
        self.stop_btn = QPushButton("停止查询")
        self.stop_btn.clicked.connect(self.stop_query)
        self.stop_btn.setEnabled(False)
        
        # 保存按钮
        self.save_btn = QPushButton("保存数据")
        self.save_btn.clicked.connect(self.save_data)
        self.save_btn.setEnabled(False)
        
        # 添加控件到控制布局
        control_layout = QHBoxLayout()
        control_layout.addWidget(self.code_label)
        control_layout.addWidget(self.code_input)
        control_layout.addWidget(self.interval_label)
        control_layout.addWidget(self.interval_combo)
        control_layout.addWidget(self.query_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.save_btn)
        
        control_group.setLayout(control_layout)
        
        # 创建状态显示区域
        status_layout = QHBoxLayout()
        self.status_label = QLabel("状态: 就绪")
        self.last_update_label = QLabel("上次更新: -")
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.last_update_label)
        
        # Data Display
        data_group = QGroupBox("实时数据")
        data_group.setStyleSheet("""
            background: white;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
        """)
        data_layout = QHBoxLayout()
        data_layout.setContentsMargins(15, 10, 15, 10)
        
        # Modern data cards
        def create_data_card(title, value_label):
            card = QWidget()
            card.setStyleSheet("""
                background: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 12px;
            """)
            layout = QVBoxLayout(card)
            title_label = QLabel(title)
            title_label.setStyleSheet("color: #606060; font-size: 14px;")
            value_label.setStyleSheet("""
                color: #202020;
                font-size: 18px;
                font-weight: 600;
                qproperty-alignment: AlignCenter;
            """)
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            return card

        self.current_value = QLabel("--")
        self.max_value = QLabel("--")
        self.min_value = QLabel("--")
        self.difference_label = QLabel("--")

        data_layout.addWidget(create_data_card("当前值", self.current_value))
        data_layout.addWidget(create_data_card("最大值", self.max_value))
        data_layout.addWidget(create_data_card("最小值", self.min_value))
        data_layout.addWidget(create_data_card("差值", self.difference_label))
        data_group.setLayout(data_layout)

        # Chart
        self.canvas = EnhancedMatplotlibCanvas()
        
        # 添加所有布局到主布局
        main_layout.addWidget(control_group)
        main_layout.addLayout(status_layout)
        main_layout.addWidget(data_group)
        main_layout.addWidget(self.canvas)
        
        # 显示窗口
        self.show()
    
    def start_query(self):
        self.stock_code = self.code_input.text().strip()
        if not self.stock_code:
            QMessageBox.warning(self, "输入错误", "请输入有效的股票代码")
            return
        
        # 获取刷新间隔
        interval_text = self.interval_combo.currentText()
        interval_seconds = int(interval_text.replace("秒", ""))
        
        # 设置定时器
        self.timer.start(interval_seconds * 1000)
        
        # 立即执行一次查询
        self.update_data()
        
        # 更新UI状态
        self.query_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.save_btn.setEnabled(True)
        self.code_input.setEnabled(False)
        self.interval_combo.setEnabled(False)
        
        self.status_label.setText(f"状态: 正在监控股票 {self.stock_code}")
    
    def stop_query(self):
        self.timer.stop()
        
        # 更新UI状态
        self.query_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.code_input.setEnabled(True)
        self.interval_combo.setEnabled(True)
        
        self.status_label.setText("状态: 已停止")
    
    def update_data(self):
        try:
            self.data = self.query.query_stock_l2_data(self.stock_code)
            if self.data:
                self.update_chart(self.data)
                self.last_update_label.setText(f"上次更新: {datetime.now().strftime('%H:%M:%S')}")
            else:
                self.status_label.setText(f"状态: 获取数据失败")
        except Exception as e:
            self.status_label.setText(f"状态: 错误 - {str(e)}")
    
    def update_chart(self, data):
        times_str, values, date_str = self.query.get_chart_data(data)
        if not times_str or not values:
            return
        
        # Convert string times to numerical values
        times_num = mdates.date2num([datetime.strptime(t, "%H:%M") for t in times_str])
        
        self.canvas.ax.clear()
        self.canvas.original_times = times_str  # Store original time strings
        
        # Plot using numerical values
        self.canvas.ax.plot(times_num, values, color='#2196F3', linewidth=2, 
                          marker='o', markersize=4, markerfacecolor='white')
        
        # Format x-axis with original time strings
        self.canvas.ax.xaxis.set_major_formatter(mdates.DateFormatter("%H:%M"))
        self.canvas.ax.set_xticks(times_num[::10])
        
        # Fill between
        self.canvas.ax.fill_between(times_num, values, 0, 
                                  where=(np.array(values) > 0),
                                  color='#4CAF50', alpha=0.1)
        self.canvas.ax.fill_between(times_num, values, 0,
                                  where=(np.array(values) <= 0),
                                  color='#F44336', alpha=0.1)
        
        # Chart annotations
        self.canvas.ax.set_title(f"股票 {self.stock_code} 大单净额走势", 
                               fontsize=16, color='#404040', pad=20)
        self.canvas.ax.set_xlabel("时间", fontsize=12, color='#606060')
        self.canvas.ax.set_ylabel("净额 (万元)", fontsize=12, color='#606060')
        
        # Grid and ticks
        self.canvas.ax.grid(True, color='#F0F0F0', linestyle='--')
        self.canvas.ax.tick_params(axis='x', colors='#808080', rotation=45)
        self.canvas.ax.tick_params(axis='y', colors='#808080')
        
        # Remove spines
        for spine in self.canvas.ax.spines.values():
            spine.set_color('#E0E0E0')
        
        # Dynamic Y axis
        y_min, y_max = min(values)*1.1, max(values)*1.1
        self.canvas.ax.set_ylim(y_min if y_min < 0 else 0, y_max if y_max > 0 else 0)
        
        self.canvas.draw()
    
    def save_data(self):
        if self.data and self.stock_code:
            self.query.save_to_file(self.data, self.stock_code)
            
            # 保存图表
            import os
            today = datetime.now().strftime("%Y%m%d")
            current_dir = os.path.dirname(os.path.abspath(__file__))
            img_path = os.path.join(current_dir, f"L2_Chart_{self.stock_code}_{today}.png")
            self.canvas.fig.savefig(img_path, dpi=300)

            QMessageBox.information(self, "保存成功", f"数据和图表已保存到当前目录")


def main():
    app = QApplication(sys.argv)
    window = StockL2App()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()