package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// 热搜数据结构
type HotSearchItem struct {
	KeyWord string `json:"KeyWord"`
	Hot     int    `json:"Hot"`
	Order   int    `json:"Order"`
	Sub     int    `json:"Sub"`
}

type HotSearchResponse struct {
	List []HotSearchItem `json:"List"`
}

// 股票L2数据结构
type StockL2Response struct {
	DaDanJinE interface{} `json:"dadanjinge"`
	StockID   string      `json:"stockid,omitempty"`
	Success   bool        `json:"success,omitempty"`
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源
	},
}

// 获取热搜数据
func getHotSearchData(c *gin.Context) {
	url := "https://apparticle.longhuvip.com/w1/api/index.php"
	
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建请求失败"})
		return
	}
	
	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	
	// 设置查询参数
	q := req.URL.Query()
	q.Add("a", "GetHotSearch")
	q.Add("apiv", "w31")
	q.Add("Type", "1")
	q.Add("c", "InteractData")
	q.Add("PhoneOSNew", "1")
	q.Add("DeviceID", "e283305172a73233")
	q.Add("VerSion", "*******")
	req.URL.RawQuery = q.Encode()
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "请求失败: " + err.Error()})
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "读取响应失败"})
		return
	}
	
	// 解析JSON
	var hotSearchResp HotSearchResponse
	if err := json.Unmarshal(body, &hotSearchResp); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解析数据失败"})
		return
	}
	
	// 返回数据
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    hotSearchResp.List,
		"count":   len(hotSearchResp.List),
		"timestamp": time.Now().Unix(),
	})
}

// 获取股票L2数据
func getStockL2Data(c *gin.Context) {
	stockCode := c.Param("code")
	if stockCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "股票代码不能为空"})
		return
	}
	
	url := "https://apphq.longhuvip.com/w1/api/index.php"
	
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建请求失败"})
		return
	}
	
	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	
	// 设置查询参数
	q := req.URL.Query()
	q.Add("a", "GetStockDaDanTrendIncremental")
	q.Add("apiv", "w31")
	q.Add("c", "StockL2Data")
	q.Add("PhoneOSNew", "1")
	q.Add("UserID", "1410355")
	q.Add("DeviceID", "e283305172a73233")
	q.Add("VerSion", "*******")
	q.Add("Token", "e52a0e9702dc3ef939fe77835d2ea694")
	q.Add("StockID", stockCode)
	req.URL.RawQuery = q.Encode()
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "请求失败: " + err.Error()})
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "读取响应失败"})
		return
	}
	
	// 解析JSON
	var stockResp StockL2Response
	if err := json.Unmarshal(body, &stockResp); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解析数据失败"})
		return
	}
	
	// 返回数据
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stockResp,
		"stockCode": stockCode,
		"timestamp": time.Now().Unix(),
	})
}

// WebSocket处理器
func handleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		fmt.Printf("WebSocket升级失败: %v\n", err)
		return
	}
	defer conn.Close()
	
	fmt.Println("WebSocket连接建立")
	
	// 心跳检测
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// 发送心跳
			if err := conn.WriteMessage(websocket.TextMessage, []byte(`{"type":"ping","timestamp":"`+fmt.Sprintf("%d", time.Now().Unix())+`"}`)); err != nil {
				fmt.Printf("发送心跳失败: %v\n", err)
				return
			}
		default:
			// 读取客户端消息
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					fmt.Printf("WebSocket错误: %v\n", err)
				}
				break
			}
			
			// 处理客户端消息
			fmt.Printf("收到消息: %s\n", message)
			
			// 回复消息
			response := fmt.Sprintf(`{"type":"response","message":"已收到消息","timestamp":"%d"}`, time.Now().Unix())
			if err := conn.WriteMessage(websocket.TextMessage, []byte(response)); err != nil {
				fmt.Printf("发送响应失败: %v\n", err)
				break
			}
		}
	}
}
