import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"Comple","apiv":"w30","Type":"a7","c":"Task","TaskID":"3","PhoneOSNew":"1","UserID":"736561","DeviceID":"37825ea487af865ee0349c458cb7925c&","Token":"ef87cb04e9633ae9a329a91080ac6d0b"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)


import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"Comple","apiv":"w30","Type":"a5","c":"Task","TaskID":"5","PhoneOSNew":"1","UserID":"736561","DeviceID":"37825ea487af865ee0349c458cb7925c","Token":"ef87cb04e9633ae9a329a91080ac6d0b"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)



import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"Comple","apiv":"w31","Type":"a2","c":"Task","TaskID":"6","PhoneOSNew":"1","UserID":"736561","DeviceID":"37825ea487af865ee0349c458cb7925c","Token":"ef87cb04e9633ae9a329a91080ac6d0b"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)



import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"ShareMsg","apiv":"w31","c":"Task","PhoneOSNew":"1","UserID":"2075379","DeviceID":"e283305172a73255","Token":"94db1204a4623a18cef3d341bc72e712"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)


#朋友圈分享
import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"Comple","apiv":"w30","Type":"a11","c":"Task","TaskID":"5","PhoneOSNew":"1","UserID":"2075379","DeviceID":"e283305172a73233","Token":"94db1204a4623a18cef3d341bc72e712"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)


import requests

url = "https://applhb.longhuvip.com/w1/api/index.php"

querystring = {"a":"Comple","apiv":"w30","Type":"a11","c":"Task","TaskID":"5","PhoneOSNew":"1","UserID":"736561","DeviceID":"37825ea487af865ee0349c458cb7925c","Token":"ef87cb04e9633ae9a329a91080ac6d0b"}

payload = "-----011000010111000001101001--\r\n\r\n"
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 11; M2012K11AC Build/RKQ1.200826.002)",
    "content-type": "multipart/form-data; boundary=---011000010111000001101001"
}

response = requests.request("POST", url, data=payload, headers=headers, params=querystring)

print(response.text)