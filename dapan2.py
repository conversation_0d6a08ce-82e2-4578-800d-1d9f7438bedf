import tkinter as tk
from tkinter import ttk
from tkinter.font import Font
from functools import partial
from datetime import datetime
import json
import requests


def timestamp_to_time(timestamp):
    return datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')


url = "https://apphwhq.longhuvip.com/w1/api/index.php"
querystring = {"a": "ZhiBoContent", "apiv": "w36", "c": "ConceptionPoint", "PhoneOSNew": "1", "DeviceID": "e283305172a73233", "VerSion": "********", "index": "0"}
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphwhq.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}
response = requests.request("POST", url, headers=headers, params=querystring)

# 解析JSON数据
data = json.loads(response.text)

# 主窗口
root = tk.Tk()
root.title("数据可视化")
root.geometry("600x400")

# 滚动条
scrollbar = ttk.Scrollbar(root, orient="vertical")
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

# Treeview
tree = ttk.Treeview(root, columns=('Time', 'Comment', 'UserName'), show='headings', yscrollcommand=scrollbar.set)
tree.heading('Time', text='时间')
tree.heading('Comment', text='评论')
tree.heading('UserName', text='用户名')

# 设置列宽度和对齐方式
tree.column('Time', width=150)
tree.column('Comment', width=300, anchor='w')  # Comment列宽度设置
tree.column('UserName', width=150, anchor='center')

# 填充数据到Treeview
for item in data['List']:
    readable_time = timestamp_to_time(item.get('Time'))
    wrapped_comment = item.get('Comment')  # 保留你的add_newlines_to_comment函数
    tree.insert('', tk.END, values=(readable_time, wrapped_comment, item.get('UserName')))

# 设置行高
s = ttk.Style()
s.configure('Treeview', rowheight=50)  # 设置Treeview的行高


# 绑定事件处理函数
def motion_handler(tree, event):
    f = Font(font='TkDefaultFont')

    def adjust_newlines(val, width, pad=10):
        if not isinstance(val, str):
            return val
        else:
            words = val.split()
            lines = [[], ]
            for word in words:
                line = lines[-1] + [word, ]
                if f.measure(' '.join(line)) < (width - pad):
                    lines[-1].append(word)
                else:
                    lines[-1] = ' '.join(lines[-1])
                    lines.append([word, ])

            if isinstance(lines[-1], list):
                lines[-1] = ' '.join(lines[-1])

            return '\n'.join(lines)
    if event is None or tree.identify_region(event.x, event.y) == "separator":
        column = "Comment"
        col_widths = [tree.column(cid)['width'] for cid in tree['columns']]

        for iid in tree.get_children():
            new_values = []
            for (value, width) in zip(tree.item(iid)['values'], col_widths):
                if column == 'Comment':
                    new_values.append(adjust_newlines(value, width))
                else:
                    new_values.append(value)
            tree.item(iid, values=new_values)






# 确保Treeview可以垂直滚动
tree.configure(yscrollcommand=scrollbar.set)
scrollbar.config(command=tree.yview)

# 绑定鼠标拖动事件以调整换行
tree.bind('<B1-Motion>', partial(motion_handler, tree))
motion_handler(tree, None)  # 初始换行处理



# 布局和启动主循环
tree.pack(expand=True, fill='both')
root.mainloop()
