#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}📊 轻量级股票监控程序 - 构建脚本${NC}"
echo "====================================="

echo
echo -e "${YELLOW}🔍 检查Go环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Go语言环境${NC}"
    echo "请先安装Go语言: https://golang.org/dl/"
    exit 1
fi

echo -e "${GREEN}✅ Go环境检查通过${NC}"
go version

echo
echo -e "${YELLOW}📦 安装依赖包...${NC}"
if ! go mod tidy; then
    echo -e "${RED}❌ 依赖包安装失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 依赖包安装完成${NC}"

echo
echo -e "${YELLOW}🔨 开始编译程序...${NC}"

# 编译当前平台版本
echo "正在编译当前平台版本..."
if ! go build -ldflags="-s -w" -o stock-monitor .; then
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 当前平台版本编译完成: stock-monitor${NC}"

echo
echo -e "${YELLOW}🌍 编译跨平台版本...${NC}"

# 编译Windows版本
echo "正在编译 Windows 版本..."
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o stock-monitor-windows.exe .

# 编译Linux版本
echo "正在编译 Linux 版本..."
GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o stock-monitor-linux .

# 编译macOS版本
echo "正在编译 macOS 版本..."
GOOS=darwin GOARCH=amd64 go build -ldflags="-s -w" -o stock-monitor-macos .

# 编译ARM版本（适用于树莓派等）
echo "正在编译 ARM 版本..."
GOOS=linux GOARCH=arm64 go build -ldflags="-s -w" -o stock-monitor-arm64 .

echo
echo -e "${BLUE}📁 生成的文件:${NC}"
ls -la stock-monitor*

echo
echo -e "${GREEN}🎉 构建完成！${NC}"
echo
echo -e "${BLUE}💡 使用方法:${NC}"
echo "   1. 运行程序: ./stock-monitor"
echo "   2. 打开浏览器访问 http://localhost:8080"
echo "   3. 开始使用股票监控功能"
echo
echo -e "${BLUE}📊 程序特点:${NC}"
echo "   - 占用内存: < 20MB"
echo "   - 启动时间: < 1秒"
echo "   - 文件大小: 5-10MB"
echo "   - 跨平台支持"
echo

# 设置执行权限
chmod +x stock-monitor*

echo -e "${GREEN}✅ 已设置执行权限${NC}"
echo
echo -e "${YELLOW}🚀 现在可以运行程序了: ./stock-monitor${NC}"
