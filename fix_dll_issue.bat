@echo off
chcp 65001 >nul
echo ===================================
echo    修复DLL依赖问题构建脚本
echo ===================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已正确安装并添加到PATH
    pause
    exit /b 1
)

echo.
echo 开始修复构建流程...
python fix_build.py

echo.
echo 如果仍有问题，请尝试以下解决方案：
echo 1. 安装 Microsoft Visual C++ Redistributable
echo 2. 使用 --onedir 模式而不是 --onefile
echo 3. 在目标机器上运行程序
echo.
echo 按任意键退出...
pause >nul
