#!/usr/bin/env python3
"""
PyInstaller 构建配置脚本
用于将Python应用打包成Windows可执行文件
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装必要的依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("依赖包安装完成!")
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False
    return True

def create_spec_files():
    """创建PyInstaller spec文件"""
    
    # HotSearchApp.spec
    hotsearch_spec = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['HotSearchApp.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'requests',
        'json',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='热搜监控',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
"""

    # StockL2App.spec  
    stock_spec = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['Da Dan L2 v2.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'requests',
        'matplotlib',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.figure',
        'matplotlib.font_manager',
        'numpy',
        'json',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票L2监控',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
"""

    # 写入spec文件
    with open("HotSearchApp.spec", "w", encoding="utf-8") as f:
        f.write(hotsearch_spec.strip())
    
    with open("StockL2App.spec", "w", encoding="utf-8") as f:
        f.write(stock_spec.strip())
    
    print("Spec文件创建完成!")

def build_executables():
    """构建可执行文件"""
    print("开始构建可执行文件...")

    try:
        # 构建热搜监控应用 - 使用更兼容的参数
        print("构建热搜监控应用...")
        subprocess.check_call([
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", "热搜监控",
            "--add-data", "requirements.txt;.",
            "--hidden-import", "PyQt5.QtCore",
            "--hidden-import", "PyQt5.QtGui",
            "--hidden-import", "PyQt5.QtWidgets",
            "--hidden-import", "requests",
            "--collect-all", "PyQt5",
            "HotSearchApp.py"
        ])

        # 构建股票L2监控应用
        print("构建股票L2监控应用...")
        subprocess.check_call([
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", "股票L2监控",
            "--add-data", "requirements.txt;.",
            "--hidden-import", "PyQt5.QtCore",
            "--hidden-import", "PyQt5.QtGui",
            "--hidden-import", "PyQt5.QtWidgets",
            "--hidden-import", "requests",
            "--hidden-import", "matplotlib",
            "--hidden-import", "matplotlib.backends.backend_qt5agg",
            "--hidden-import", "numpy",
            "--collect-all", "PyQt5",
            "--collect-all", "matplotlib",
            "Da Dan L2 v2.py"
        ])

        print("构建完成!")
        print("可执行文件位置:")
        print("- 热搜监控: dist/热搜监控.exe")
        print("- 股票L2监控: dist/股票L2监控.exe")

    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False

    return True

def main():
    """主函数"""
    print("=== Python应用打包工具 ===")
    print("正在准备打包环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 创建spec文件
    create_spec_files()
    
    # 构建可执行文件
    if build_executables():
        print("\n=== 打包完成! ===")
        print("您现在可以在dist目录中找到可执行文件")
    else:
        print("\n=== 打包失败! ===")

if __name__ == "__main__":
    main()
