# 股票监控应用 - 简单解决方案

## 使用方法

### 第一次使用
1. 双击 `安装依赖.bat` 安装必要的Python包
2. 双击 `启动应用.bat` 选择要运行的应用

### 日常使用
- 双击 `启动应用.bat` 选择应用
- 或直接双击 `热搜监控.bat` 或 `股票L2监控.bat`

## 优势
- 无DLL问题 - 直接运行Python脚本
- 体积小 - 只有几个文件
- 易维护 - 代码清晰可见
- 兼容性好 - 支持所有Python版本
- 启动快 - 无需解压或加载大文件

## 系统要求
- Python 3.7或更高版本
- 网络连接（用于获取数据）

## 故障排除

### 如果提示"找不到Python"
1. 下载并安装Python：https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"

### 如果依赖包安装失败
1. 检查网络连接
2. 以管理员身份运行"安装依赖.bat"
3. 手动安装：`pip install PyQt5 requests matplotlib numpy`

---
**版本**: 简单解决方案 v1.0
**特点**: 无DLL问题，最大兼容性
