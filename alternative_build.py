#!/usr/bin/env python3
"""
替代构建方案 - 使用目录模式避免DLL问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_directory_version():
    """构建目录版本（更稳定）"""
    print("使用目录模式构建应用...")
    
    # 清理旧文件
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # 构建热搜监控应用（目录模式）
    print("构建热搜监控应用（目录模式）...")
    cmd1 = [
        "pyinstaller",
        "--onedir",  # 使用目录模式
        "--windowed",
        "--name", "热搜监控",
        "--noconfirm",
        "--clean",
        "--exclude-module", "pandas",
        "--exclude-module", "IPython",
        "--exclude-module", "matplotlib",
        "--exclude-module", "numpy",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui", 
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "requests",
        "HotSearchApp.py"
    ]
    
    try:
        subprocess.check_call(cmd1)
        print("热搜监控应用构建成功!")
    except subprocess.CalledProcessError as e:
        print(f"热搜监控应用构建失败: {e}")
    
    # 构建股票L2监控应用（目录模式）
    print("构建股票L2监控应用（目录模式）...")
    cmd2 = [
        "pyinstaller",
        "--onedir",  # 使用目录模式
        "--windowed", 
        "--name", "股票L2监控",
        "--noconfirm",
        "--clean",
        "--exclude-module", "pandas",
        "--exclude-module", "IPython",
        "--exclude-module", "sphinx",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "PyQt5.QtWidgets", 
        "--hidden-import", "requests",
        "--hidden-import", "matplotlib.backends.backend_qt5agg",
        "--hidden-import", "numpy",
        "Da Dan L2 v2.py"
    ]
    
    try:
        subprocess.check_call(cmd2)
        print("股票L2监控应用构建成功!")
    except subprocess.CalledProcessError as e:
        print(f"股票L2监控应用构建失败: {e}")

def create_launcher_scripts():
    """创建启动脚本"""
    print("创建启动脚本...")
    
    # 热搜监控启动脚本
    hotsearch_launcher = """@echo off
cd /d "%~dp0"
cd "热搜监控"
start "" "热搜监控.exe"
"""
    
    # 股票L2监控启动脚本
    stock_launcher = """@echo off
cd /d "%~dp0"
cd "股票L2监控"
start "" "股票L2监控.exe"
"""
    
    with open("dist/启动热搜监控.bat", "w", encoding="gbk") as f:
        f.write(hotsearch_launcher)
    
    with open("dist/启动股票L2监控.bat", "w", encoding="gbk") as f:
        f.write(stock_launcher)
    
    print("启动脚本创建完成!")

def create_readme():
    """创建使用说明"""
    readme_content = """
# 股票监控应用 - 目录版本

## 使用方法

### 方式1：直接运行
1. 进入对应的应用目录
2. 双击 .exe 文件运行

### 方式2：使用启动脚本（推荐）
1. 双击 "启动热搜监控.bat" 启动热搜监控
2. 双击 "启动股票L2监控.bat" 启动股票L2监控

## 目录结构
```
dist/
├── 启动热搜监控.bat
├── 启动股票L2监控.bat
├── 热搜监控/
│   ├── 热搜监控.exe
│   └── [依赖文件...]
└── 股票L2监控/
    ├── 股票L2监控.exe
    └── [依赖文件...]
```

## 注意事项
1. 请保持目录结构完整，不要移动exe文件
2. 首次运行可能需要允许通过防火墙
3. 需要网络连接获取实时数据

## 优势
- 避免DLL依赖问题
- 启动速度更快
- 更稳定可靠
"""
    
    with open("dist/使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("=== 替代构建方案 - 目录模式 ===")
    
    # 安装依赖
    print("安装PyInstaller...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=6.0"])
    
    # 构建应用
    build_directory_version()
    
    # 创建启动脚本
    create_launcher_scripts()
    
    # 创建说明文件
    create_readme()
    
    print("\n=== 构建完成! ===")
    print("应用位置: dist/ 目录")
    print("使用启动脚本运行应用，避免DLL问题")

if __name__ == "__main__":
    main()
