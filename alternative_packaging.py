#!/usr/bin/env python3
"""
替代打包方案 - 使用多种工具尝试构建exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """清理构建文件"""
    print("清理构建文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__', 'output']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")

def install_tools():
    """安装打包工具"""
    print("安装打包工具...")
    tools = [
        "cx_Freeze",
        "auto-py-to-exe", 
        "nuitka",
        "briefcase"
    ]
    
    for tool in tools:
        try:
            print(f"安装 {tool}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", tool])
            print(f"✅ {tool} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {tool} 安装失败")

def method1_cx_freeze():
    """方法1: 使用cx_Freeze"""
    print("\n=== 方法1: 使用cx_Freeze ===")
    
    # 创建setup.py文件
    setup_content = '''
import sys
from cx_Freeze import setup, Executable

# 依赖包
packages = ["PyQt5", "requests", "json", "datetime", "os", "sys"]

# 包含的文件
include_files = []

# 排除的模块
excludes = ["matplotlib", "numpy", "pandas", "IPython", "sphinx", "tkinter"]

# 构建选项
build_exe_options = {
    "packages": packages,
    "excludes": excludes,
    "include_files": include_files,
    "optimize": 2,
}

# 可执行文件配置
executables = [
    Executable(
        "HotSearchApp.py",
        base="Win32GUI" if sys.platform == "win32" else None,
        target_name="热搜监控_CX.exe",
        icon=None
    ),
    Executable(
        "Da Dan L2 v2.py", 
        base="Win32GUI" if sys.platform == "win32" else None,
        target_name="股票L2监控_CX.exe",
        icon=None
    )
]

setup(
    name="股票监控应用",
    version="1.0",
    description="股票监控应用 - cx_Freeze版本",
    options={"build_exe": build_exe_options},
    executables=executables,
)
'''
    
    with open("setup_cx.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    try:
        print("使用cx_Freeze构建...")
        subprocess.check_call([sys.executable, "setup_cx.py", "build"])
        print("✅ cx_Freeze构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ cx_Freeze构建失败: {e}")
        return False

def method2_nuitka():
    """方法2: 使用Nuitka"""
    print("\n=== 方法2: 使用Nuitka ===")
    
    try:
        # 构建热搜监控
        print("使用Nuitka构建热搜监控...")
        cmd1 = [
            sys.executable, "-m", "nuitka",
            "--standalone",
            "--windows-disable-console", 
            "--output-filename=热搜监控_Nuitka.exe",
            "--output-dir=nuitka_output",
            "--remove-output",
            "HotSearchApp.py"
        ]
        subprocess.check_call(cmd1)
        print("✅ 热搜监控 Nuitka构建成功!")
        
        # 构建股票监控
        print("使用Nuitka构建股票监控...")
        cmd2 = [
            sys.executable, "-m", "nuitka",
            "--standalone",
            "--windows-disable-console",
            "--output-filename=股票L2监控_Nuitka.exe", 
            "--output-dir=nuitka_output",
            "--remove-output",
            "Da Dan L2 v2.py"
        ]
        subprocess.check_call(cmd2)
        print("✅ 股票L2监控 Nuitka构建成功!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka构建失败: {e}")
        return False

def method3_portable_python():
    """方法3: 创建便携式Python版本"""
    print("\n=== 方法3: 创建便携式Python版本 ===")
    
    try:
        # 创建便携式目录
        portable_dir = Path("portable_python")
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        portable_dir.mkdir()
        
        # 创建启动脚本
        launcher_content = '''@echo off
cd /d "%~dp0"
echo 启动股票监控应用...
echo.
echo 1. 热搜监控
echo 2. 股票L2监控  
echo 3. 退出
echo.
set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    python HotSearchApp.py
) else if "%choice%"=="2" (
    python "Da Dan L2 v2.py"
) else if "%choice%"=="3" (
    exit
) else (
    echo 无效选择
    pause
)
'''
        
        with open(portable_dir / "启动应用.bat", "w", encoding="gbk") as f:
            f.write(launcher_content)
        
        # 复制Python文件
        python_files = ["HotSearchApp.py", "Da Dan L2 v2.py", "Da Dan L2.py"]
        for file in python_files:
            if os.path.exists(file):
                shutil.copy2(file, portable_dir)
        
        # 创建requirements.txt
        shutil.copy2("requirements.txt", portable_dir)
        
        # 创建安装脚本
        install_script = '''@echo off
echo 安装依赖包...
pip install -r requirements.txt
echo 安装完成!
pause
'''
        
        with open(portable_dir / "安装依赖.bat", "w", encoding="gbk") as f:
            f.write(install_script)
        
        # 创建说明文件
        readme = '''# 便携式Python版本

## 使用方法
1. 确保系统已安装Python 3.7+
2. 双击"安装依赖.bat"安装依赖包
3. 双击"启动应用.bat"运行应用

## 优势
- 无DLL问题
- 体积小
- 易于维护
- 兼容性好
'''
        
        with open(portable_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme)
        
        print("✅ 便携式Python版本创建成功!")
        print(f"位置: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 便携式版本创建失败: {e}")
        return False

def method4_batch_wrapper():
    """方法4: 创建批处理包装器"""
    print("\n=== 方法4: 创建批处理包装器 ===")
    
    try:
        # 创建批处理包装器目录
        wrapper_dir = Path("batch_wrapper")
        if wrapper_dir.exists():
            shutil.rmtree(wrapper_dir)
        wrapper_dir.mkdir()
        
        # 热搜监控包装器
        hotsearch_wrapper = '''@echo off
title 热搜监控
cd /d "%~dp0"
python HotSearchApp.py
if errorlevel 1 (
    echo.
    echo 运行出错，请检查Python环境和依赖包
    pause
)
'''
        
        # 股票监控包装器
        stock_wrapper = '''@echo off
title 股票L2监控
cd /d "%~dp0"
python "Da Dan L2 v2.py"
if errorlevel 1 (
    echo.
    echo 运行出错，请检查Python环境和依赖包
    pause
)
'''
        
        with open(wrapper_dir / "热搜监控.bat", "w", encoding="gbk") as f:
            f.write(hotsearch_wrapper)
            
        with open(wrapper_dir / "股票L2监控.bat", "w", encoding="gbk") as f:
            f.write(stock_wrapper)
        
        # 复制Python文件
        python_files = ["HotSearchApp.py", "Da Dan L2 v2.py", "requirements.txt"]
        for file in python_files:
            if os.path.exists(file):
                shutil.copy2(file, wrapper_dir)
        
        print("✅ 批处理包装器创建成功!")
        print(f"位置: {wrapper_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 批处理包装器创建失败: {e}")
        return False

def create_summary():
    """创建总结报告"""
    summary = '''# 替代打包方案总结

## 可用方案

### 1. cx_Freeze版本
- 位置: build/exe.win-amd64-3.x/
- 特点: 传统打包工具，相对稳定
- 使用: 直接运行exe文件

### 2. Nuitka版本  
- 位置: nuitka_output/
- 特点: 编译为C++，性能最好
- 使用: 直接运行exe文件

### 3. 便携式Python版本
- 位置: portable_python/
- 特点: 无DLL问题，需要Python环境
- 使用: 运行"启动应用.bat"

### 4. 批处理包装器
- 位置: batch_wrapper/
- 特点: 最简单，最稳定
- 使用: 双击对应的bat文件

## 推荐使用顺序
1. 批处理包装器 (最稳定)
2. 便携式Python版本 (无DLL问题)
3. Nuitka版本 (性能最好)
4. cx_Freeze版本 (传统方案)

## 注意事项
- 所有方案都需要目标机器有Python环境
- 批处理方案最简单可靠
- 如需真正的独立exe，推荐Nuitka
'''
    
    with open("打包方案总结.md", "w", encoding="utf-8") as f:
        f.write(summary)

def main():
    """主函数"""
    print("=== 替代打包方案 ===")
    print("尝试多种工具构建exe，避免PyInstaller的DLL问题")
    
    # 清理环境
    clean_build()
    
    # 安装工具
    install_tools()
    
    success_count = 0
    
    # 尝试各种方法
    if method1_cx_freeze():
        success_count += 1
    
    if method2_nuitka():
        success_count += 1
    
    if method3_portable_python():
        success_count += 1
    
    if method4_batch_wrapper():
        success_count += 1
    
    # 创建总结
    create_summary()
    
    print(f"\n=== 构建完成! 成功创建 {success_count} 种方案 ===")
    print("推荐使用批处理包装器方案，最稳定可靠!")

if __name__ == "__main__":
    main()
