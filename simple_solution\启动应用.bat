@echo off
title 股票监控应用
cd /d "%~dp0"
echo ===============================================
echo              股票监控应用
echo ===============================================
echo.
echo 选择要启动的应用：
echo.
echo 1. 热搜监控
echo 2. 股票L2监控
echo 3. 安装依赖包
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    call "热搜监控.bat"
) else if "%choice%"=="2" (
    call "股票L2监控.bat"
) else if "%choice%"=="3" (
    call "安装依赖.bat"
) else if "%choice%"=="4" (
    exit /b 0
) else (
    echo 无效选择
    pause
)
