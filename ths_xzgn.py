import requests
from datetime import datetime
import time

# 获取当前时间戳
current_time = int(datetime.now().timestamp())

url = "https://dq.10jqka.com.cn/fuyao/concept_express/concept_trends/v1/list"

payload = {
    "page_num": 1,
    "page_size": 15,
    "concept": "all",
    "date": current_time,  # 使用当前时间戳替换固定值
    "tab_type": "all"
}
headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.54 Mobile Safari/537.36 Hexin_Gphone/11.04.02 (Royal Flush) hxtheme/0 innerversion/G037.08.920.1.32 followPhoneSystemTheme/0 userid/423324878 getHXAPPAccessibilityMode/0 hxNewFont/1 isVip/0 getHXAPPFontSetting/small getHXAPPAdaptOldSetting/0",
    "Host": "dq.10jqka.com.cn",
    "Connection": "keep-alive",
    "sec-ch-ua": "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"",
    "Accept": "application/json, text/plain, */*",
    "withCreditials": "true",
    "Content-Type": "application/json",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "Origin": "https://eq.10jqka.com.cn",
    "X-Requested-With": "com.hexin.plat.android",
    "Sec-Fetch-Site": "same-site",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://eq.10jqka.com.cn/",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    "Cookie": "user=MDptb180MjMzMjQ4Nzg6Ok5vbmU6NTAwOjQzMzMyNDg3ODo3LDExMTExMTExMTExLDQwOzQ0LDExLDQwOzYsMSw0MDs1LDEsNDA7MSwxMDEsNDA7MiwxLDQwOzMsMSw0MDs1LDEsNDA7OCwwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMSw0MDsxMDIsMSw0MDoyNzo6OjQyMzMyNDg3ODoxNzE1MTQ3MjM4Ojo6MTUxMDU1MTA2MDoyNjc4NDAwOjA6MTBlZDJiZTFhMzkwMTliODE0ZTFhZjI1YjdiZGJjNjZiOjox; userid=423324878; u_name=mo_423324878; escapename=mo_423324878; ticket=ffb9f99a6ab9fcb8c513e9de3fe98dc0; user_status=0; IFUserCookieKey={\"escapename\":\"mo_423324878\",\"userid\":\"423324878\"}; hxmPid=ths_mob_gainiansudi; v=A4YodNA6HK58E8jKEjrCut7Y1ncI58qpnCn-BXCvciCFGikt2HcasWy7ThND"
}


response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)


#新增概念




import requests

url = "http://eq.10jqka.com.cn/webpage/concept-change/index.html"
headers = {
    'User-Agent': "Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.54 Mobile Safari/537.36 Hexin_Gphone/11.04.03 (Royal Flush) hxtheme/0 innerversion/G037.08.921.1.32 followPhoneSystemTheme/0 userid/423324878 getHXAPPAccessibilityMode/0 hxNewFont/1 isVip/0 getHXAPPFontSetting/small getHXAPPAdaptOldSetting/0",
    'Host': "eq.10jqka.com.cn",
    'Connection': "keep-alive",
    'sec-ch-ua': "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"",
    'sec-ch-ua-mobile': "?1",
    'sec-ch-ua-platform': "\"Android\"",
    'Upgrade-Insecure-Requests': "1",
    'Accept': "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    'X-Requested-With': "com.hexin.plat.android",
    'Sec-Fetch-Site': "none",
    'Sec-Fetch-Mode': "navigate",
    'Sec-Fetch-User': "?1",
    'Sec-Fetch-Dest': "document",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    'Cookie': "voiceStatus=open; THSSESSID=a95a757bedbd0208819a092162; user=MDptb180MjMzMjQ4Nzg6Ok5vbmU6NTAwOjQzMzMyNDg3ODo3LDExMTExMTExMTExLDQwOzQ0LDExLDQwOzYsMSw0MDs1LDEsNDA7MSwxMDEsNDA7MiwxLDQwOzMsMSw0MDs1LDEsNDA7OCwwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMSw0MDsxMDIsMSw0MDoyNzo6OjQyMzMyNDg3ODoxNzE1MTQ3MjM4Ojo6MTUxMDU1MTA2MDoyNjc4NDAwOjA6MTBlZDJiZTFhMzkwMTliODE0ZTFhZjI1YjdiZGJjNjZiOjox; userid=423324878; u_name=mo_423324878; escapename=mo_423324878; ticket=ffb9f99a6ab9fcb8c513e9de3fe98dc0; user_status=0; IFUserCookieKey={\"escapename\":\"mo_423324878\",\"userid\":\"423324878\"}; hxmPid=ths_mob_gainiansudi_ceptMore_show; v=A1X7mZdvn9Cv5bustlolpFvFZVoPUglk0wbtuNf6EUwbLnqAn6IZNGNW_Ypk",
    'If-None-Match': "\"65d5bfbb-932\"",
    'If-Modified-Since': "Wed, 21 Feb 2024 09:17:47 GMT"
}


response = requests.request("GET", url, headers=headers)

print(response.text)



import requests

url = "https://eq.10jqka.com.cn/webpage/concept-change/index.html"

response = requests.get(url, headers=headers)
response2 = requests.get(url, headers=headers)

# 打印响应内容
print(response.text)

