from pptx import Presentation
from pptx.util import Inches

# 创建一个PPT文件
ppt = Presentation()

# 添加封面幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[0])
title = slide.shapes.title
subtitle = slide.placeholders[1]
title.text = "公共关系行业分类"
subtitle.text = "探讨公关行业的多样性\n2024年6月3日"

# 添加目录幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "目录"
content = slide.placeholders[1]
content.text = "1. 按服务对象分类\n2. 按工作性质分类\n3. 按职能领域分类\n4. 按传播渠道分类\n5. 按行业领域分类"

# 添加“按服务对象分类”幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "按服务对象分类"
content = slide.placeholders[1]
content.text = (
    "- 企业公关\n"
    "  - 品牌管理\n"
    "  - 产品公关\n"
    "  - 员工关系\n"
    "- 政府公关\n"
    "  - 政策宣传\n"
    "  - 公共事务\n"
    "  - 危机管理\n"
    "- 非营利组织公关\n"
    "  - 筹款活动\n"
    "  - 志愿者管理\n"
    "  - 公益宣传\n"
    "- 个人公关\n"
    "  - 名人公关\n"
    "  - 政治公关"
)

# 添加“按工作性质分类”幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "按工作性质分类"
content = slide.placeholders[1]
content.text = "- 媒体关系\n- 危机管理\n- 事件策划\n- 市场传播\n- 内部沟通"

# 添加“按职能领域分类”幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "按职能领域分类"
content = slide.placeholders[1]
content.text = "- 金融公关\n- 医疗公关\n- 科技公关\n- 消费品公关\n- 环境公关"

# 添加“按传播渠道分类”幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "按传播渠道分类"
content = slide.placeholders[1]
content.text = "- 传统媒体公关\n- 数字公关\n- 内容营销\n- 在线声誉管理"

# 添加“按行业领域分类”幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "按行业领域分类"
content = slide.placeholders[1]
content.text = "- 娱乐公关\n- 体育公关\n- 教育公关"

# 添加总结幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "总结"
content = slide.placeholders[1]
content.text = (
    "- 公共关系行业多样化\n"
    "- 不同分类满足不同需求\n"
    "- 公关在各个领域的重要性"
)

# 添加问答环节幻灯片
slide = ppt.slides.add_slide(ppt.slide_layouts[1])
title = slide.shapes.title
title.text = "问答环节"
content = slide.placeholders[1]
content.text = "提问时间"

# 保存PPT
ppt_path = "公共关系行业分类.pptx"
ppt.save(ppt_path)

ppt_path