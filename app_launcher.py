#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦应用启动器
提供多个版本的选择界面
"""

import sys
import os
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QGroupBox, 
                            QMessageBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap, QPainter

class AppLauncher(QMainWindow):
    """应用启动器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📊 开盘啦应用启动器")
        self.setGeometry(200, 200, 800, 600)
        
        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("📊 开盘啦监控应用集合")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2196F3;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel("选择您要使用的监控应用版本")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #666666;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(desc_label)
        
        # 应用选择区域
        apps_layout = QHBoxLayout()
        apps_layout.setSpacing(20)
        
        # 美化统一版
        unified_group = self.create_app_group(
            "🎨 美化统一版",
            "现代化界面 • 高性能 • 功能整合",
            ["✨ 现代化UI设计", "⚡ 高性能架构", "🔄 智能数据管理", "📊 统一界面"],
            "#4CAF50",
            self.launch_unified_app
        )
        apps_layout.addWidget(unified_group)
        
        # 原版应用
        original_group = self.create_app_group(
            "📱 原版应用",
            "经典界面 • 稳定可靠 • 分离运行",
            ["🔥 热搜监控", "📈 股票L2监控", "💾 数据导出", "⏰ 定时刷新"],
            "#2196F3",
            self.show_original_options
        )
        apps_layout.addWidget(original_group)
        
        layout.addLayout(apps_layout)
        
        # 便携版本
        portable_group = QGroupBox("💼 便携版本（无需Python环境）")
        portable_layout = QVBoxLayout()
        
        portable_desc = QLabel("已打包的exe文件，可直接运行")
        portable_desc.setStyleSheet("color: #666666; margin-bottom: 10px;")
        portable_layout.addWidget(portable_desc)
        
        portable_buttons = QHBoxLayout()
        
        hot_exe_btn = QPushButton("🔥 热搜监控.exe")
        hot_exe_btn.clicked.connect(self.launch_hot_exe)
        hot_exe_btn.setMinimumHeight(40)
        
        stock_exe_btn = QPushButton("📈 股票L2监控.exe")
        stock_exe_btn.clicked.connect(self.launch_stock_exe)
        stock_exe_btn.setMinimumHeight(40)
        
        portable_buttons.addWidget(hot_exe_btn)
        portable_buttons.addWidget(stock_exe_btn)
        portable_layout.addLayout(portable_buttons)
        
        portable_group.setLayout(portable_layout)
        layout.addWidget(portable_group)
        
        # 系统信息
        info_text = QTextEdit()
        info_text.setMaximumHeight(120)
        info_text.setReadOnly(True)
        info_text.setText(self.get_system_info())
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(info_text)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FAFAFA;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #E0E0E0;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        
    def create_app_group(self, title, subtitle, features, color, callback):
        """创建应用组"""
        group = QGroupBox(title)
        layout = QVBoxLayout()
        
        # 副标题
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet(f"color: {color}; font-weight: normal; margin-bottom: 10px;")
        layout.addWidget(subtitle_label)
        
        # 特性列表
        for feature in features:
            feature_label = QLabel(feature)
            feature_label.setStyleSheet("color: #666666; margin: 2px 0;")
            layout.addWidget(feature_label)
            
        # 启动按钮
        launch_btn = QPushButton("🚀 启动应用")
        launch_btn.clicked.connect(callback)
        launch_btn.setMinimumHeight(45)
        launch_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                font-size: 14px;
                margin-top: 15px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """)
        layout.addWidget(launch_btn)
        
        group.setLayout(layout)
        return group
        
    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#4CAF50": "#388E3C",
            "#2196F3": "#1976D2",
            "#FF9800": "#F57C00"
        }
        return color_map.get(color, color)
        
    def get_system_info(self):
        """获取系统信息"""
        info = []
        info.append(f"Python版本: {sys.version.split()[0]}")
        info.append(f"工作目录: {os.getcwd()}")
        
        # 检查依赖包
        try:
            import PyQt5
            info.append(f"PyQt5: ✅ 已安装")
        except ImportError:
            info.append(f"PyQt5: ❌ 未安装")
            
        try:
            import requests
            info.append(f"requests: ✅ 已安装")
        except ImportError:
            info.append(f"requests: ❌ 未安装")
            
        # 检查文件存在性
        files_to_check = [
            "unified_monitoring_app.py",
            "HotSearchApp.py", 
            "Da Dan L2 v2.py",
            "portable_version/热搜监控.exe",
            "portable_version/股票L2监控.exe"
        ]
        
        for file in files_to_check:
            if os.path.exists(file):
                info.append(f"{file}: ✅")
            else:
                info.append(f"{file}: ❌")
                
        return "\n".join(info)
        
    def launch_unified_app(self):
        """启动美化统一版应用"""
        try:
            if os.path.exists("run_unified_app.py"):
                subprocess.Popen([sys.executable, "run_unified_app.py"])
                self.show_message("启动成功", "美化统一版应用正在启动...")
            else:
                self.show_error("文件不存在", "找不到 run_unified_app.py 文件")
        except Exception as e:
            self.show_error("启动失败", f"启动美化统一版失败:\n{str(e)}")
            
    def show_original_options(self):
        """显示原版应用选项"""
        from PyQt5.QtWidgets import QDialog, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("选择原版应用")
        dialog.setFixedSize(400, 200)
        
        layout = QVBoxLayout(dialog)
        
        layout.addWidget(QLabel("请选择要启动的原版应用:"))
        
        hot_btn = QPushButton("🔥 热搜监控 (HotSearchApp.py)")
        hot_btn.clicked.connect(lambda: self.launch_original_app("HotSearchApp.py", dialog))
        
        stock_btn = QPushButton("📈 股票L2监控 (Da Dan L2 v2.py)")
        stock_btn.clicked.connect(lambda: self.launch_original_app("Da Dan L2 v2.py", dialog))
        
        layout.addWidget(hot_btn)
        layout.addWidget(stock_btn)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Cancel)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        dialog.exec_()
        
    def launch_original_app(self, filename, dialog=None):
        """启动原版应用"""
        try:
            if os.path.exists(filename):
                subprocess.Popen([sys.executable, filename])
                self.show_message("启动成功", f"{filename} 正在启动...")
                if dialog:
                    dialog.accept()
            else:
                self.show_error("文件不存在", f"找不到 {filename} 文件")
        except Exception as e:
            self.show_error("启动失败", f"启动 {filename} 失败:\n{str(e)}")
            
    def launch_hot_exe(self):
        """启动热搜监控exe"""
        exe_path = "portable_version/热搜监控.exe"
        if os.path.exists(exe_path):
            try:
                subprocess.Popen([exe_path])
                self.show_message("启动成功", "热搜监控.exe 正在启动...")
            except Exception as e:
                self.show_error("启动失败", f"启动exe失败:\n{str(e)}")
        else:
            self.show_error("文件不存在", f"找不到 {exe_path}")
            
    def launch_stock_exe(self):
        """启动股票L2监控exe"""
        exe_path = "portable_version/股票L2监控.exe"
        if os.path.exists(exe_path):
            try:
                subprocess.Popen([exe_path])
                self.show_message("启动成功", "股票L2监控.exe 正在启动...")
            except Exception as e:
                self.show_error("启动失败", f"启动exe失败:\n{str(e)}")
        else:
            self.show_error("文件不存在", f"找不到 {exe_path}")
            
    def show_message(self, title, message):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
        
    def show_error(self, title, message):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    launcher = AppLauncher()
    launcher.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
