import tkinter as tk
from tkinter import ttk
from tkinter.font import Font
from functools import partial
from datetime import datetime
import json
import requests

url = "https://apphwhq.longhuvip.com/w1/api/index.php"
querystring = {"a": "ZhiBoContent", "apiv": "w36", "c": "ConceptionPoint", "PhoneOSNew": "1", "DeviceID": "e283305172a73233", "VerSion": "********", "index": "0"}
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphwhq.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}

# 新增的函数
def timestamp_to_time(timestamp):
    return datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')

def fetch_theme_data():
    import requests
    url = "http://applhb.longhuvip.com/w1/api/index.php"
    querystring = {"a": "InfoList", "apiv": ["w36", "w36"], "c": ["Theme", "Theme"], "PhoneOSNew": ["1", "1"],
                   "UserID": ["2075379", "2075379"], "DeviceID": ["e283305172a73233", "e283305172a73233"],
                   "VerSion": ["********", "********"],
                   "Token": ["94db1204a4623a18cef3d341bc72e712", "94db1204a4623a18cef3d341bc72e712"]}
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Host": "applhb.longhuvip.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }
    response = requests.request("POST", url, headers=headers, params=querystring)
    response_data = json.loads(response.text)
    if response.status_code == 200:
        result = "\n".join(
            f"{item['Name']}: {datetime.fromtimestamp(int(item['CreateTime'])).strftime('%Y-%m-%d %H:%M:%S')} 涨停：{item['ZTNum']}  上涨：{item['UpNum']}"
            for item in response_data["New"]
        )
    else:
        result = None

    theme_data_label.config(text=result)


    return result

def fetch_emotion_data():
    import requests

    url = "https://apphq.longhuvip.com/w1/api/index.php"

    querystring = {"a": "ChangeStatistics", "apiv": "w31", "c": "HomeDingPan", "PhoneOSNew": "1", "UserID": "1410355",
                   "DeviceID": "e283305172a73233", "Token": "e52a0e9702dc3ef939fe77835d2ea694"}

    payload = "-----011000010111000001101001--\r\n\r\n"
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******",
        "content-type": "multipart/form-data; boundary=---011000010111000001101001"
    }

    response = requests.request("POST", url, data=payload, headers=headers, params=querystring)
    response_data = response.json()
    # 提取数据
    result = response_data if response.status_code == 200 else "请求失败"
    ztjs = response_data["info"][0]["ztjs"]
    strong = response_data["info"][0]["strong"]
    lbgd = response_data["info"][0]["lbgd"]
    day = response_data["info"][0]["Day"]
    df_num = response_data["info"][0]["df_num"]
    tip = "H(75) / L(25)"
    emotion_data_label.config(text=json.dumps(result, indent=2))  # 显示JSON数据
    emotion_data_label.config(text=f"情绪: {strong} 连板: {lbgd} 涨停: {ztjs} 大面: {df_num} \n {day} {tip}")

    return result

def fetch_data(auto=True):
    """Fetch and update data, optionally indicating if called automatically."""
    global last_fetch_time  # 假设last_fetch_time是一个记录上次自动刷新时间的全局变量
    try:
        response = requests.request("POST", url, headers=headers, params=querystring)
        data = json.loads(response.text)

        # Only perform automatic fetch if enough time has passed since the last fetch.
        current_time = datetime.now()
        if auto and (current_time - last_fetch_time).seconds < 3:  # Example: Refresh every 3 seconds
            return
        last_fetch_time = current_time

        # Clear and refill Treeview with new data
        tree.delete(*tree.get_children())
        for item in data['List']:
            readable_time = timestamp_to_time(item.get('Time'))
            #wrapped_comment = add_newlines_to_comment(item.get('Comment'))
            wrapped_comment = item.get('Comment')
            tree.insert('', tk.END, values=(readable_time, item.get('UserName'), wrapped_comment))

    except Exception as e:
        print(f"Error fetching data: {e}")
    fetch_emotion_data()
    fetch_theme_data()


def refresh_button_clicked():
    """Callback for the manual refresh button."""
    fetch_data(auto=True)  # Manually triggered fetch


#
# response = requests.request("POST", url, headers=headers, params=querystring)
#
# # 解析JSON数据
# data = json.loads(response.text)

#
# def fetch_data():
#     response = requests.request("POST", url, headers=headers, params=querystring)
#
#     # 解析JSON数据
#     data = json.loads(response.text)
#
#     # 清空当前Treeview的数据
#     tree.delete(*tree.get_children())
#
#     # 填充新的数据到Treeview
#     for item in data['List']:
#         readable_time = timestamp_to_time(item.get('Time'))
#         wrapped_comment = add_newlines_to_comment(item.get('Comment'))  # 添加换行符
#         tree.insert('', tk.END, values=(readable_time, item.get('UserName'), wrapped_comment))
#
#     # 再次设置定时器，每3秒调用fetch_data()
#     #root.after(3000, fetch_data)  # 3000毫秒即3秒

# 创建主窗口，并设置起始大小
root = tk.Tk()
root.title("大盘")
root.geometry("600x600")  # 设置窗口的宽和高

# 设置窗口图标（如果有一个.ico文件）
# icon = tk.PhotoImage(file='your_icon.ico')
# root.iconphoto(True, icon)

emotion_data_label = tk.Label(root, text="", wraplength=400)  # 创建一个Label来显示情感数据
emotion_data_label.pack(side=tk.BOTTOM, pady=10)  # 在刷新按钮下方添加
theme_data_label = tk.Label(root, text="", wraplength=400, anchor=tk.CENTER)  # 创建一个Label来显示情感数据
theme_data_label.pack(side=tk.BOTTOM, pady=10)  # 在刷新按钮下方添加


# 创建一个滚动条
scrollbar = ttk.Scrollbar(root, orient="vertical")
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

# 创建一个Treeview控件用于显示数据，并绑定滚动条
tree = ttk.Treeview(root, columns=('Time', 'UserName', 'Comment'), show='headings', yscrollcommand=scrollbar.set)
tree.heading('Time', text='时间')
tree.heading('UserName', text='用户名')
tree.heading('Comment', text='评论')

# 修改split_comment函数，只在超过长度时添加换行符
def add_newlines_to_comment(comment, max_length=35):
    lines = []
    current_line = ''
    for char in comment:
        if len(current_line) + 1 > max_length:
            lines.append(current_line)
            current_line = char
        else:
            current_line += char
    lines.append(current_line)
    return '\n'.join(lines)


h_scrollbar = ttk.Scrollbar(root, orient="horizontal")
h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
tree.configure(xscrollcommand=h_scrollbar.set)
h_scrollbar.config(command=tree.xview)

# v_scrollbar = ttk.Scrollbar(root, orient="vertical")
# v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
# tree.configure(xscrollcommand=v_scrollbar.set)
# v_scrollbar.config(command=tree.xview)

# 设置字体样式
#font_style = font.Font(family="Arial", size=12, weight="bold")
# 填充数据到Treeview
fetch_data()


# 填充数据到Treeview
# for item in data['List']:
#     readable_time = timestamp_to_time(item.get('Time'))
#     tree.insert('', tk.END, values=(item.get('UID'), readable_time, item.get('Comment'), item.get('UserName')))
#     #tree.item(tree.index(tk.END), tags='wrap')  # 应用自动换行标签

# 配置Treeview控件的显示属性
tree.pack(expand=True, fill='both')
#tree.configure(font=font_style)  # 设置字体样式
#tree.column('UID', width=120, anchor='e')  # 设置列宽和对齐方式
tree.column('Time', width=130, stretch=tk.NO)
#tree.column('Comment', anchor='w')
tree.column('UserName', width=70, anchor='center', stretch=tk.NO)  # 设置居中对齐

# 设置自动换行
#tree.tag_configure('wrap', wrap='word', justify='left')  # 设置标签自动换行
tree.column('Comment', width=5000, minwidth=300, anchor='w', stretch=tk.YES)

# 确保Treeview可以垂直滚动 无用
tree.configure(yscrollcommand=scrollbar.set)
scrollbar.config(command=tree.yview)

# Add manual refresh button
refresh_button = ttk.Button(root, text="刷新", command=refresh_button_clicked)
refresh_button.pack(side=tk.BOTTOM, pady=10)

# Start the automatic refresh timer
last_fetch_time = datetime.now()  # Initialize last fetch time
root.after(3000, lambda: fetch_data(auto=True))  # 3000毫秒即3秒后首次自动刷新

# 启动GUI主循环
root.mainloop()