#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票L2监控组件 - 高性能版本
基于Da Dan L2 v2.py重构为高性能Widget组件
"""

import sys
import requests
import json
import numpy as np
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QPushButton, QLabel, QLineEdit, QComboBox, 
                            QGroupBox, QMessageBox, QProgressBar, QCheckBox)
from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal, QSettings, QMutex
from PyQt5.QtGui import QIntValidator, QFont
import os

# 尝试导入pyqtgraph，如果失败则使用matplotlib
try:
    import pyqtgraph as pg
    from pyqtgraph import PlotWidget
    USE_PYQTGRAPH = True
except ImportError:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    USE_PYQTGRAPH = False


class StockL2API(QThread):
    """股票L2 API异步请求类"""
    
    data_received = pyqtSignal(dict, str)  # data, stock_code
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://apphq.longhuvip.com/w1/api/index.php"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.default_params = {
            "a": "GetStockDaDanTrendIncremental",
            "apiv": "w31",
            "c": "StockL2Data",
            "PhoneOSNew": "1",
            "UserID": "1410355",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******",
            "Token": "e52a0e9702dc3ef939fe77835d2ea694"
        }
        self.mutex = QMutex()
        self.running = False
        self.stock_code = ""
        
    def set_stock_code(self, stock_code):
        """设置股票代码"""
        self.stock_code = stock_code
        
    def run(self):
        """执行API请求"""
        self.mutex.lock()
        try:
            if not self.running or not self.stock_code:
                return
                
            params = self.default_params.copy()
            params["StockID"] = self.stock_code
            
            response = requests.get(self.base_url, params=params, 
                                  headers=self.headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            self.data_received.emit(data, self.stock_code)
            
        except requests.exceptions.RequestException as e:
            self.error_occurred.emit(f"网络请求错误: {str(e)}")
        except json.JSONDecodeError as e:
            self.error_occurred.emit(f"数据解析错误: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"未知错误: {str(e)}")
        finally:
            self.mutex.unlock()
            
    def start_request(self):
        """开始请求"""
        self.running = True
        if not self.isRunning():
            self.start()
            
    def stop_request(self):
        """停止请求"""
        self.running = False
        if self.isRunning():
            self.quit()
            self.wait(3000)


class HighPerformanceChart(QWidget):
    """高性能图表组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_chart()
        
    def init_chart(self):
        """初始化图表"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        if USE_PYQTGRAPH:
            # 使用pyqtgraph（高性能）
            self.plot_widget = PlotWidget()
            self.plot_widget.setBackground('w')
            self.plot_widget.setLabel('left', '大单净额', units='万元')
            self.plot_widget.setLabel('bottom', '时间')
            self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
            
            # 设置样式
            self.plot_widget.getAxis('left').setPen(pg.mkPen(color='black', width=1))
            self.plot_widget.getAxis('bottom').setPen(pg.mkPen(color='black', width=1))
            
            layout.addWidget(self.plot_widget)
            self.curve = None
            
        else:
            # 使用matplotlib（兼容性）
            self.figure = Figure(figsize=(12, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            self.ax = self.figure.add_subplot(111)
            
            layout.addWidget(self.canvas)
            
    def update_chart(self, times, values, stock_code):
        """更新图表数据"""
        if not times or not values:
            return
            
        if USE_PYQTGRAPH:
            self.plot_widget.clear()
            
            # 转换时间为数值
            time_nums = list(range(len(times)))
            
            # 绘制主曲线
            self.curve = self.plot_widget.plot(time_nums, values, 
                                             pen=pg.mkPen(color='blue', width=2),
                                             symbol='o', symbolSize=4,
                                             symbolBrush='white')
            
            # 填充区域
            brush_pos = pg.mkBrush(color=(255, 0, 0, 50))  # 红色半透明
            brush_neg = pg.mkBrush(color=(0, 255, 0, 50))  # 绿色半透明
            
            # 分别填充正负区域
            pos_values = [max(0, v) for v in values]
            neg_values = [min(0, v) for v in values]
            
            self.plot_widget.plot(time_nums, pos_values, fillLevel=0, 
                                brush=brush_pos, pen=None)
            self.plot_widget.plot(time_nums, neg_values, fillLevel=0, 
                                brush=brush_neg, pen=None)
            
            # 设置X轴标签
            x_ticks = []
            step = max(1, len(times) // 10)  # 最多显示10个标签
            for i in range(0, len(times), step):
                x_ticks.append((i, times[i]))
            
            self.plot_widget.getAxis('bottom').setTicks([x_ticks])
            
        else:
            # matplotlib版本
            self.ax.clear()
            
            # 绘制主曲线
            self.ax.plot(times, values, 'b-', linewidth=2, marker='o', 
                        markersize=4, markerfacecolor='white')
            
            # 填充区域
            self.ax.fill_between(times, values, 0, 
                               where=(np.array(values) > 0),
                               color='red', alpha=0.2)
            self.ax.fill_between(times, values, 0,
                               where=(np.array(values) <= 0),
                               color='green', alpha=0.2)
            
            self.ax.set_xlabel('时间')
            self.ax.set_ylabel('大单净额 (万元)')
            self.ax.set_title(f'{stock_code} 大单净额趋势')
            self.ax.grid(True, alpha=0.3)
            
            # 设置X轴标签
            step = max(1, len(times) // 10)
            self.ax.set_xticks(times[::step])
            self.ax.set_xticklabels(times[::step], rotation=45)
            
            self.figure.tight_layout()
            self.canvas.draw()


class StockL2Widget(QWidget):
    """股票L2监控Widget组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = StockL2API()
        self.timer = QTimer()
        self.settings = QSettings('StockL2Widget', 'Settings')
        self.data_cache = None
        self.stock_code = ""
        self.previous_value = None

        # 集成数据管理器
        try:
            from data_manager import get_stock_l2_manager
            self.data_manager = get_stock_l2_manager()
            self.use_data_manager = True
        except ImportError:
            self.data_manager = None
            self.use_data_manager = False

        self.init_ui()
        self.setup_connections()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 页面标题
        title_widget = self.create_page_title()
        layout.addWidget(title_widget)

        # 控制面板卡片
        control_card = QGroupBox("⚙️ 监控设置")
        control_layout = QGridLayout()
        control_layout.setSpacing(15)

        # 股票代码输入
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("💼 请输入6位股票代码，如: 002334")
        self.code_input.setValidator(QIntValidator())
        self.code_input.setMaxLength(6)
        self.code_input.setMinimumHeight(45)

        # 刷新间隔选择
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["⚡ 5秒", "🔄 10秒", "⏱️ 15秒", "⏰ 30秒", "🕐 60秒"])
        self.interval_combo.setCurrentText("🔄 10秒")
        self.interval_combo.setMinimumHeight(45)

        # 控制按钮
        self.start_btn = QPushButton("🚀 开始监控")
        self.start_btn.setMinimumHeight(45)
        self.start_btn.setProperty("class", "success")

        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setMinimumHeight(45)
        self.stop_btn.setProperty("class", "error")

        self.save_btn = QPushButton("💾 保存数据")
        self.save_btn.setMinimumHeight(45)
        self.save_btn.setProperty("class", "primary")
        
        # 自动保存选项
        self.auto_save_cb = QCheckBox("💾 自动保存")

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMinimumHeight(6)

        # 布局控制面板
        control_layout.addWidget(QLabel("📊 股票代码:"), 0, 0)
        control_layout.addWidget(self.code_input, 0, 1)
        control_layout.addWidget(QLabel("⏱️ 刷新间隔:"), 0, 2)
        control_layout.addWidget(self.interval_combo, 0, 3)
        control_layout.addWidget(self.start_btn, 0, 4)
        control_layout.addWidget(self.stop_btn, 0, 5)
        control_layout.addWidget(self.save_btn, 0, 6)
        control_layout.addWidget(self.auto_save_cb, 1, 0)
        control_layout.addWidget(self.progress_bar, 1, 1, 1, 6)
        control_card.setLayout(control_layout)
        
        # 实时数据仪表盘
        dashboard_card = self.create_dashboard_card()

        # 图表区域
        self.chart = HighPerformanceChart()

        # 状态信息
        status_card = self.create_status_card()

        # 组装主布局
        layout.addWidget(control_card)
        layout.addWidget(dashboard_card)
        layout.addWidget(self.chart)
        layout.addWidget(status_card)

    def create_page_title(self):
        """创建页面标题"""
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)

        # 主标题
        title_label = QLabel("📈 股票L2监控")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 700;
                color: #4CAF50;
                margin: 0;
            }
        """)

        # 描述
        desc_label = QLabel("实时监控股票大单净额数据")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #757575;
                margin-left: 10px;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        title_layout.addStretch()

        return title_widget

    def create_dashboard_card(self):
        """创建仪表盘卡片"""
        dashboard_card = QGroupBox("📊 实时数据仪表盘")
        dashboard_layout = QHBoxLayout()
        dashboard_layout.setSpacing(20)

        # 当前值卡片
        self.current_value = self.create_metric_card("📈", "当前值", "--", "#2196F3")

        # 最大值卡片
        self.max_value = self.create_metric_card("⬆️", "最大值", "--", "#4CAF50")

        # 最小值卡片
        self.min_value = self.create_metric_card("⬇️", "最小值", "--", "#FF9800")

        # 差值卡片
        self.difference_label = self.create_metric_card("📊", "变化", "--", "#9C27B0")

        dashboard_layout.addWidget(self.current_value)
        dashboard_layout.addWidget(self.max_value)
        dashboard_layout.addWidget(self.min_value)
        dashboard_layout.addWidget(self.difference_label)

        dashboard_card.setLayout(dashboard_layout)
        return dashboard_card

    def create_status_card(self):
        """创建状态卡片"""
        status_card = QGroupBox("🔄 监控状态")
        status_layout = QHBoxLayout()
        status_layout.setSpacing(20)

        # 状态指示器
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 16px;
                background-color: #E8F5E8;
                border-radius: 8px;
                color: #2E7D32;
            }
        """)

        # 更新时间
        self.update_time_label = QLabel("🕒 最后更新: --")
        self.update_time_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                padding: 8px 16px;
                background-color: #E3F2FD;
                border-radius: 8px;
                color: #1976D2;
            }
        """)

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.update_time_label)

        status_card.setLayout(status_layout)
        return status_card
        
    def create_metric_card(self, icon, title, value, color):
        """创建指标卡片"""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 12px;
                padding: 16px;
                min-width: 150px;
                min-height: 100px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
                margin: 0;
            }}
        """)

        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #757575;
                font-weight: 500;
                margin: 0;
            }
        """)

        # 数值
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: 700;
                color: {color};
                margin: 0;
            }}
        """)

        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(value_label)

        # 保存value_label的引用以便后续更新
        card.value_label = value_label

        return card
        
    def setup_connections(self):
        """设置信号连接"""
        self.start_btn.clicked.connect(self.start_monitoring)
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.save_btn.clicked.connect(self.save_data)
        self.code_input.returnPressed.connect(self.start_monitoring)
        
        # API信号连接
        self.api.data_received.connect(self.on_data_received)
        self.api.error_occurred.connect(self.on_error_occurred)
        
        # 定时器连接
        self.timer.timeout.connect(self.request_data)
        
    def start_monitoring(self):
        """开始监控"""
        stock_code = self.code_input.text().strip()
        if not stock_code:
            QMessageBox.warning(self, "警告", "请输入股票代码")
            return
            
        if len(stock_code) != 6:
            QMessageBox.warning(self, "警告", "股票代码应为6位数字")
            return
            
        self.stock_code = stock_code
        self.api.set_stock_code(stock_code)
        
        # 获取刷新间隔
        interval_text = self.interval_combo.currentText()
        interval = int(interval_text.replace("秒", "")) * 1000
        
        # 启动定时器
        self.timer.start(interval)
        
        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.code_input.setEnabled(False)
        self.interval_combo.setEnabled(False)
        
        self.status_label.setText(f"正在监控 {stock_code}")
        
        # 立即请求一次数据
        self.request_data()
        
    def stop_monitoring(self):
        """停止监控"""
        self.timer.stop()
        self.api.stop_request()
        
        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.code_input.setEnabled(True)
        self.interval_combo.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.status_label.setText("监控已停止")
        
    def request_data(self):
        """请求数据"""
        if self.use_data_manager and self.data_manager:
            # 使用数据管理器
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.data_manager.update_data(force_refresh=True, stock_code=self.stock_code)
        else:
            # 使用原始API
            if self.api.isRunning():
                return

            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.api.start_request()
        
    def on_data_received(self, data, stock_code):
        """处理接收到的数据"""
        self.progress_bar.setVisible(False)
        self.data_cache = data
        
        # 更新实时数据显示
        self.update_data_display(data)
        
        # 更新图表
        self.update_chart(data, stock_code)
        
        # 更新状态
        self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
        
        # 自动保存
        if self.auto_save_cb.isChecked():
            self.save_data()
            
    def on_error_occurred(self, error_msg):
        """处理错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"错误: {error_msg}")
        
    def update_data_display(self, data):
        """更新数据显示"""
        if not data or "dadanjinge" not in data:
            return
            
        dadan_data = data["dadanjinge"]
        if not dadan_data:
            return
            
        values = [item[1] / 10000 for item in dadan_data]  # 转换为万元
        
        current = values[-1] if values else 0
        max_val = max(values) if values else 0
        min_val = min(values) if values else 0

        # 更新仪表盘数值
        self.current_value.value_label.setText(f"{current:.2f}万")
        self.max_value.value_label.setText(f"{max_val:.2f}万")
        self.min_value.value_label.setText(f"{min_val:.2f}万")
        
        # 计算差值并更新显示
        if self.previous_value is not None:
            difference = current - self.previous_value
            difference_text = f"{difference:+.2f}万"

            # 根据变化幅度设置颜色
            if abs(difference) > 100:
                # 大幅变化 - 红色警告
                self.difference_label.setStyleSheet("""
                    QWidget {
                        background-color: white;
                        border: 2px solid #F44336;
                        border-radius: 12px;
                        padding: 16px;
                        min-width: 150px;
                        min-height: 100px;
                    }
                """)
                color = "#F44336"
            elif difference > 0:
                # 上涨 - 绿色
                color = "#4CAF50"
            else:
                # 下跌 - 橙色
                color = "#FF9800"

            self.difference_label.value_label.setText(difference_text)
            self.difference_label.value_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 18px;
                    font-weight: 700;
                    color: {color};
                    margin: 0;
                }}
            """)
        else:
            self.difference_label.value_label.setText("--")
            
        self.previous_value = current
        
    def update_chart(self, data, stock_code):
        """更新图表"""
        times, values = self.get_chart_data(data)
        if times and values:
            self.chart.update_chart(times, values, stock_code)
            
    def get_chart_data(self, data):
        """获取图表数据"""
        if not data or "dadanjinge" not in data:
            return None, None
            
        dadan_data = data["dadanjinge"]
        if not dadan_data:
            return None, None
            
        times = [item[0] for item in dadan_data]
        values = [item[1] / 10000 for item in dadan_data]  # 转换为万元
        
        return times, values
        
    def save_data(self):
        """保存数据"""
        if not self.data_cache or not self.stock_code:
            QMessageBox.warning(self, "警告", "没有可保存的数据")
            return
            
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            today = datetime.now().strftime("%Y%m%d")
            
            # 保存JSON数据
            json_filename = os.path.join(current_dir, f"L2_Data_{self.stock_code}_{today}.json")
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(self.data_cache, f, ensure_ascii=False, indent=4)
                
            QMessageBox.information(self, "保存成功", f"数据已保存到:\n{json_filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存失败:\n{str(e)}")
            
    def load_settings(self):
        """加载设置"""
        stock_code = self.settings.value('stock_code', '')
        if stock_code:
            self.code_input.setText(stock_code)
            
        interval = self.settings.value('interval', '10秒')
        index = self.interval_combo.findText(interval)
        if index >= 0:
            self.interval_combo.setCurrentIndex(index)
            
        auto_save = self.settings.value('auto_save', False, type=bool)
        self.auto_save_cb.setChecked(auto_save)
        
    def save_settings(self):
        """保存设置"""
        self.settings.setValue('stock_code', self.code_input.text())
        self.settings.setValue('interval', self.interval_combo.currentText())
        self.settings.setValue('auto_save', self.auto_save_cb.isChecked())
        
    def stop_timers(self):
        """停止所有定时器"""
        self.timer.stop()
        self.api.stop_request()
        
    def refresh_data(self):
        """刷新数据（外部调用接口）"""
        if self.timer.isActive():
            self.request_data()
        else:
            QMessageBox.information(self, "提示", "请先开始监控")
            
    def export_data(self):
        """导出数据（外部调用接口）"""
        self.save_data()
        
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        self.stop_timers()
        event.accept()
