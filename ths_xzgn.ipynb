{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "outputs": [{"ename": "TypeError", "evalue": "'Response' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 50\u001b[0m\n\u001b[0;32m     38\u001b[0m \u001b[38;5;66;03m# 示例调用\u001b[39;00m\n\u001b[0;32m     39\u001b[0m response_data \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     40\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus_code\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m0\u001b[39m,\n\u001b[0;32m     41\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     47\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus_msg\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msuccess\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     48\u001b[0m }\n\u001b[1;32m---> 50\u001b[0m result \u001b[38;5;241m=\u001b[39m process_response(response)\n", "Cell \u001b[1;32mIn[2], line 29\u001b[0m, in \u001b[0;36mprocess_response\u001b[1;34m(response)\u001b[0m\n\u001b[0;32m     28\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mprocess_response\u001b[39m(response):\n\u001b[1;32m---> 29\u001b[0m     total \u001b[38;5;241m=\u001b[39m response[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtotal\u001b[39m\u001b[38;5;124m'\u001b[39m]  \u001b[38;5;66;03m# 获取'total'值\u001b[39;00m\n\u001b[0;32m     30\u001b[0m     flag \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m total \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m30\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0\u001b[39m  \u001b[38;5;66;03m# 如果'total'大于30，flag设为1，否则设为0\u001b[39;00m\n\u001b[0;32m     31\u001b[0m     new_concept \u001b[38;5;241m=\u001b[39m response[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnew_concept\u001b[39m\u001b[38;5;124m'\u001b[39m, [])  \u001b[38;5;66;03m# 获取'new_concept'列表\u001b[39;00m\n", "\u001b[1;31mTypeError\u001b[0m: 'Response' object is not subscriptable"]}], "source": ["import requests\n", "\n", "url = \"http://dq.10jqka.com.cn/fuyao/concept_express/new_concept/v1/list\"\n", "\n", "querystring = {\"page_num\":\"1\",\"page_size\":\"15\"}\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.54 Mobile Safari/537.36 Hexin_Gphone/11.04.02 (Royal Flush) hxtheme/0 innerversion/G037.08.920.1.32 followPhoneSystemTheme/0 userid/423324878 getHXAPPAccessibilityMode/0 hxNewFont/1 isVip/0 getHXAPPFontSetting/small getHXAPPAdaptOldSetting/0\",\n", "    \"Host\": \"dq.10jqka.com.cn\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"sec-ch-ua\": \"\\\"Chromium\\\";v=\\\"124\\\", \\\"Android WebView\\\";v=\\\"124\\\", \\\"Not-<PERSON><PERSON>\\\";v=\\\"99\\\"\",\n", "    \"Accept\": \"application/json, text/plain, */*\",\n", "    \"withCreditials\": \"true\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"sec-ch-ua-mobile\": \"?1\",\n", "    \"sec-ch-ua-platform\": \"\\\"Android\\\"\",\n", "    \"Origin\": \"https://eq.10jqka.com.cn\",\n", "    \"X-Requested-With\": \"com.hexin.plat.android\",\n", "    \"Sec-Fetch-Site\": \"same-site\",\n", "    \"Sec-Fetch-Mode\": \"cors\",\n", "    \"Sec-Fetch-Dest\": \"empty\",\n", "    \"Referer\": \"https://eq.10jqka.com.cn/\",\n", "    \"Accept-Encoding\": \"gzip, deflate, br, zstd\",\n", "    \"Accept-Language\": \"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7\",\n", "    \"Cookie\": \"user=MDptb180MjMzMjQ4Nzg6Ok5vbmU6NTAwOjQzMzMyNDg3ODo3LDExMTExMTExMTExLDQwOzQ0LDExLDQwOzYsMSw0MDs1LDEsNDA7MSwxMDEsNDA7MiwxLDQwOzMsMSw0MDs1LDEsNDA7OCwwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMSw0MDsxMDIsMSw0MDoyNzo6OjQyMzMyNDg3ODoxNzE1MTQ3MjM4Ojo6MTUxMDU1MTA2MDoyNjc4NDAwOjA6MTBlZDJiZTFhMzkwMTliODE0ZTFhZjI1YjdiZGJjNjZiOjox; userid=423324878; u_name=mo_423324878; escapename=mo_423324878; ticket=ffb9f99a6ab9fcb8c513e9de3fe98dc0; user_status=0; IFUserCookieKey={\\\"escapename\\\":\\\"mo_423324878\\\",\\\"userid\\\":\\\"423324878\\\"}; hxmPid=ths_mob_gainiansudi; v=A4YodNA6HK58E8jKEjrCut7Y1ncI58qpnCn-BXCvciCFGikt2HcasWy7ThND\"\n", "}\n", "response = requests.request(\"GET\", url, headers=headers, params=querystring)\n", "\n", "import json\n", "def process_response(response):\n", "    res = json.loads(response.text)\n", "    total = res['data']['total']  # 获取'total'值\n", "    flag = 1 if total > 30 else 0  # 如果'total'大于30，flag设为1，否则设为0\n", "    new_concept = res['data'].get('new_concept', [])  # 获取'new_concept'列表\n", "\n", "    if flag==1:  # 检查new_concept是否非空\n", "        return new_concept[0]  # 返回第一个元素\n", "    else:\n", "        return None  # 如果new_concept为空，返回None\n", "\n", "from datetime import datetime\n", "result = process_response(response)\n", "if result is not None:\n", "    name = result['concept']['name']\n", "    desp_content = f\"{result['reason']} {result['news_url']}\"\n", "    created_at_readable = datetime.fromtimestamp(result['created_at'] / 1000.0).strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "    push_url = f'https://sctapi.ftqq.com/SCT247967T74n5PeusVgZuxghdJ6uAoi8D/send?title={created_at_readable} - {name}&desp={desp_content}'\n", "\n", "    response2 = requests.get(push_url)\n", "    "], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-15T18:18:20.929484Z", "start_time": "2024-05-15T18:18:20.306462Z"}}, "id": "f9b9ef740f56467d", "execution_count": 2}, {"cell_type": "code", "outputs": [{"ename": "SyntaxError", "evalue": "f-string: unmatched '[' (*********.py, line 43)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[3], line 43\u001b[1;36m\u001b[0m\n\u001b[1;33m    push_url = f'https://sctapi.ftqq.com/******/send?title={result['concept']['name']}&desp={result[\"news_url\"]}'\u001b[0m\n\u001b[1;37m                                                                    ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m f-string: unmatched '['\n"]}], "source": [], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-15T18:28:01.568436Z", "start_time": "2024-05-15T18:28:01.560775Z"}}, "id": "d5228870245d55f6", "execution_count": 3}, {"cell_type": "markdown", "source": ["可视化"], "metadata": {"collapsed": false}, "id": "506ff41e524bbf74"}, {"cell_type": "code", "outputs": [], "source": ["\n", "#可视化界面\n", "\n", "import tkinter as tk\n", "from tkinter import messagebox\n", "\n", "def display_data():\n", "    # 假设response是一个字典，包含'status_code'和'data'字段\n", "    response = {\n", "        \"status_code\": 0,\n", "        \"data\": {\n", "            \"total\": 30,\n", "            \"new_concept\": [\n", "                # ... 新概念数据 ...\n", "            ]\n", "        },\n", "        \"status_msg\": \"success\"\n", "    }\n", "\n", "    # 创建主窗口\n", "    window = tk.Tk()\n", "    window.title(\"新闻概览\")\n", "\n", "    # 创建滚动文本框\n", "    text_box = tk.Text(window, wrap=\"word\", width=80, height=20)\n", "    text_box.pack(fill=\"both\", expand=True)\n", "\n", "    # 将数据写入文本框\n", "    for i, concept in enumerate(response[\"data\"][\"new_concept\"], start=1):\n", "        text_box.insert(tk.<PERSON><PERSON>, f\"\\n\\n{i}. {concept['reason']}\\n\\n\")\n", "        text_box.insert(tk.<PERSON><PERSON>, f\"概念: {concept['concept']['name']}\\n\")\n", "        text_box.insert(tk.END, f\"创建时间: {datetime.fromtimestamp(concept['created_at'] / 1000.0)}\\n\")\n", "        text_box.insert(tk.<PERSON><PERSON>, f\"链接: {concept['news_url']}\\n\\n\")\n", "\n", "    # 添加关闭按钮\n", "    close_button = tk.But<PERSON>(window, text=\"关闭\", command=window.destroy)\n", "    close_button.pack()\n", "\n", "    # 显示窗口\n", "    window.mainloop()\n", "\n", "# 运行程序\n", "display_data()"], "metadata": {"collapsed": false}, "id": "1683de3f3bd24c27"}, {"cell_type": "markdown", "source": [], "metadata": {"collapsed": false}, "id": "fb9930c6061602d1"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}