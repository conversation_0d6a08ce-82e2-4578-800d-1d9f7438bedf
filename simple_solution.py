#!/usr/bin/env python3
"""
最简单的解决方案 - 创建批处理启动器
避免所有打包工具的复杂性和DLL问题
"""

import os
import shutil
from pathlib import Path

def create_simple_solution():
    """创建最简单的解决方案"""
    print("=== 创建最简单的解决方案 ===")
    
    # 创建解决方案目录
    solution_dir = Path("simple_solution")
    if solution_dir.exists():
        shutil.rmtree(solution_dir)
    solution_dir.mkdir()
    
    # 复制Python文件
    python_files = [
        "HotSearchApp.py",
        "Da Dan L2 v2.py", 
        "Da Dan L2.py",
        "requirements.txt"
    ]
    
    for file in python_files:
        if os.path.exists(file):
            shutil.copy2(file, solution_dir)
            print(f"已复制: {file}")
    
    # 创建热搜监控启动器
    hotsearch_launcher = '''@echo off
title 热搜监控
cd /d "%~dp0"
echo ===============================================
echo              热搜监控应用
echo ===============================================
echo.
echo 正在启动热搜监控应用...
echo 如果出现错误，请确保已安装Python和依赖包
echo.
python HotSearchApp.py
if errorlevel 1 (
    echo.
    echo ❌ 运行失败！
    echo.
    echo 可能的解决方案：
    echo 1. 确保已安装Python 3.7+
    echo 2. 运行"安装依赖.bat"安装依赖包
    echo 3. 检查防火墙设置
    echo.
    pause
) else (
    echo.
    echo ✅ 应用已正常退出
)
'''
    
    # 创建股票监控启动器
    stock_launcher = '''@echo off
title 股票L2监控
cd /d "%~dp0"
echo ===============================================
echo             股票L2监控应用
echo ===============================================
echo.
echo 正在启动股票L2监控应用...
echo 如果出现错误，请确保已安装Python和依赖包
echo.
python "Da Dan L2 v2.py"
if errorlevel 1 (
    echo.
    echo ❌ 运行失败！
    echo.
    echo 可能的解决方案：
    echo 1. 确保已安装Python 3.7+
    echo 2. 运行"安装依赖.bat"安装依赖包
    echo 3. 检查防火墙设置
    echo.
    pause
) else (
    echo.
    echo ✅ 应用已正常退出
)
'''
    
    # 创建主启动器
    main_launcher = '''@echo off
chcp 65001 >nul
title 股票监控应用
cd /d "%~dp0"
echo ===============================================
echo              股票监控应用
echo ===============================================
echo.
echo 选择要启动的应用：
echo.
echo 1. 热搜监控
echo 2. 股票L2监控
echo 3. 安装依赖包
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    call "热搜监控.bat"
) else if "%choice%"=="2" (
    call "股票L2监控.bat"
) else if "%choice%"=="3" (
    call "安装依赖.bat"
) else if "%choice%"=="4" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择，请重新运行
    pause
)
'''
    
    # 创建依赖安装器
    install_deps = '''@echo off
title 安装依赖包
cd /d "%~dp0"
echo ===============================================
echo              安装依赖包
echo ===============================================
echo.
echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 未找到Python！
    echo.
    echo 请先安装Python 3.7或更高版本：
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Python环境正常
echo.
echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo ❌ 依赖包安装失败！
    echo.
    echo 请尝试：
    echo 1. 检查网络连接
    echo 2. 使用管理员权限运行
    echo 3. 手动安装：pip install PyQt5 requests matplotlib numpy
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 依赖包安装成功！
echo.
echo 现在可以运行应用了
pause
'''
    
    # 写入启动器文件
    with open(solution_dir / "热搜监控.bat", "w", encoding="utf-8") as f:
        f.write(hotsearch_launcher)

    with open(solution_dir / "股票L2监控.bat", "w", encoding="utf-8") as f:
        f.write(stock_launcher)

    with open(solution_dir / "启动应用.bat", "w", encoding="utf-8") as f:
        f.write(main_launcher)

    with open(solution_dir / "安装依赖.bat", "w", encoding="utf-8") as f:
        f.write(install_deps)
    
    # 创建使用说明
    readme_content = '''# 股票监控应用 - 简单解决方案

## 🎯 这是什么？
这是一个避免所有DLL问题的最简单解决方案。
不使用任何复杂的打包工具，直接运行Python脚本。

## 🚀 使用方法

### 第一次使用
1. 双击 `安装依赖.bat` 安装必要的Python包
2. 双击 `启动应用.bat` 选择要运行的应用

### 日常使用
- 双击 `启动应用.bat` 选择应用
- 或直接双击 `热搜监控.bat` 或 `股票L2监控.bat`

## ✅ 优势
- ✅ **无DLL问题** - 直接运行Python脚本
- ✅ **体积小** - 只有几个文件
- ✅ **易维护** - 代码清晰可见
- ✅ **兼容性好** - 支持所有Python版本
- ✅ **启动快** - 无需解压或加载大文件

## 📋 系统要求
- Python 3.7或更高版本
- 网络连接（用于获取数据）

## 🔧 故障排除

### 如果提示"找不到Python"
1. 下载并安装Python：https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"

### 如果依赖包安装失败
1. 检查网络连接
2. 以管理员身份运行"安装依赖.bat"
3. 手动安装：`pip install PyQt5 requests matplotlib numpy`

### 如果应用运行出错
1. 确保已安装所有依赖包
2. 检查防火墙设置
3. 查看错误信息并搜索解决方案

## 📞 技术支持
这是最稳定的解决方案，如果仍有问题：
1. 检查Python版本是否正确
2. 确保网络连接正常
3. 尝试重新安装依赖包

---
**版本**: 简单解决方案 v1.0
**特点**: 无DLL问题，最大兼容性
'''
    
    with open(solution_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # 创建快速测试脚本
    test_script = '''@echo off
echo 测试Python环境...
python -c "import sys; print(f'Python版本: {sys.version}')"
echo.
echo 测试依赖包...
python -c "import PyQt5; print('✅ PyQt5 可用')" 2>nul || echo "❌ PyQt5 未安装"
python -c "import requests; print('✅ requests 可用')" 2>nul || echo "❌ requests 未安装"
python -c "import matplotlib; print('✅ matplotlib 可用')" 2>nul || echo "❌ matplotlib 未安装"
python -c "import numpy; print('✅ numpy 可用')" 2>nul || echo "❌ numpy 未安装"
echo.
pause
'''
    
    with open(solution_dir / "测试环境.bat", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print(f"\n✅ 简单解决方案创建完成！")
    print(f"📁 位置: {solution_dir}")
    print(f"🚀 使用: 双击 '{solution_dir}/启动应用.bat'")
    
    return True

def main():
    """主函数"""
    print("创建最简单、最稳定的解决方案...")
    print("避免所有打包工具的复杂性和DLL问题")
    
    if create_simple_solution():
        print("\n=== 简单解决方案创建成功! ===")
        print("这是最稳定的方案，推荐使用！")
        print("\n使用步骤：")
        print("1. 进入 simple_solution 目录")
        print("2. 双击 '安装依赖.bat' (仅第一次)")
        print("3. 双击 '启动应用.bat' 选择应用")

if __name__ == "__main__":
    main()
