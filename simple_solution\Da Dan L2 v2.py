import requests
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import numpy as np
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QLabel, QLineEdit, QWidget, QComboBox, 
                            QMessageBox, QGroupBox, QGridLayout, QStatusBar)
from PyQt5.QtCore import QTimer, Qt, QSettings
from PyQt5.QtGui import QIntValidator
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

class StockL2DataQuery:
    def __init__(self):
        self.base_url = "https://apphq.longhuvip.com/w1/api/index.php"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.default_params = {
            "a": "GetStockDaDanTrendIncremental",
            "apiv": "w31",
            "c": "StockL2Data",
            "PhoneOSNew": "1",
            "UserID": "1410355",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******",
            "Token": "e52a0e9702dc3ef939fe77835d2ea694"
        }
    
    def query_stock_l2_data(self, stock_code):
        params = self.default_params.copy()
        params["StockID"] = stock_code
        
        try:
            response = requests.get(self.base_url, params=params, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求错误: {e}")
            return None
    
    def save_to_file(self, data, stock_code):
        import os
        today = datetime.now().strftime("%Y%m%d")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = os.path.join(current_dir, f"L2_Data_{stock_code}_{today}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return filename
    
    def get_chart_data(self, data):
        if not data or "dadanjinge" not in data:
            return None, None, None
        dadan_data = data["dadanjinge"]
        times = [item[0] for item in dadan_data]
        values = [item[1] / 10000 for item in dadan_data]
        date_str = data.get("day", datetime.now().strftime("%Y%m%d"))
        return times, values, date_str
    
    def analyze_dadan_trend(self, data):
        if not data or "dadanjinge" not in data:
            return {}
        dadan_data = data["dadanjinge"]
        current_value = dadan_data[-1][1] if dadan_data else 0
        return {
            "current_value": current_value,
            "max_value": max(item[1] for item in dadan_data) if dadan_data else 0,
            "min_value": min(item[1] for item in dadan_data) if dadan_data else 0,
            "total_trend": current_value - dadan_data[0][1] if dadan_data else 0
        }

class EnhancedMatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=12, height=6, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.ax = self.fig.add_subplot(111)
        self.setParent(parent)
        self.setMinimumSize(800, 400)
        self.fig.subplots_adjust(left=0.08, right=0.95, top=0.95, bottom=0.15)
        
        try:
            import os
            # 尝试多个可能的中文字体路径
            font_paths = [
                r"C:\Windows\Fonts\simhei.ttf",
                r"C:\Windows\Fonts\msyh.ttc",
                r"C:\Windows\Fonts\simsun.ttc"
            ]
            font_found = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    self.font = FontProperties(fname=font_path)
                    font_found = True
                    break
            if not font_found:
                self.font = FontProperties()
        except:
            self.font = FontProperties()

class StockL2App(QMainWindow):
    def __init__(self):
        super().__init__()
        self.settings = QSettings("StockMonitor", "L2Data")
        self.query = StockL2DataQuery()
        self.init_ui()
        self.load_settings()
        
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)

    def init_ui(self):
        self.setWindowTitle("股票L2大单数据监控专业版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 主控件和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # 控制面板
        control_group = QGroupBox("控制设置")
        control_layout = QGridLayout()
        
        # 股票代码输入
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("输入6位股票代码")
        self.code_input.setMaxLength(6)
        self.code_input.setValidator(QIntValidator(0, 999999))
        
        # 刷新间隔选择
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["5秒", "10秒", "30秒", "1分钟", "5分钟"])
        
        # 控制按钮
        self.start_btn = QPushButton("▶ 开始监控", clicked=self.start_query)
        self.stop_btn = QPushButton("■ 停止", clicked=self.stop_query)
        self.save_btn = QPushButton("💾 保存数据", clicked=self.save_data)
        self.stop_btn.setEnabled(False)
        
        # 实时数据面板
        data_group = QGroupBox("实时数据")
        data_layout = QHBoxLayout()
        self.current_value = self.create_data_label("--")
        self.max_value = self.create_data_label("--")
        self.min_value = self.create_data_label("--")
        data_layout.addWidget(QLabel("当前值:"))
        data_layout.addWidget(self.current_value)
        data_layout.addWidget(QLabel("最大值:"))
        data_layout.addWidget(self.max_value)
        data_layout.addWidget(QLabel("最小值:"))
        data_layout.addWidget(self.min_value)
        self.difference_label = self.create_data_label("差值: --")
        data_layout.addWidget(self.difference_label)
        data_group.setLayout(data_layout)
        
        # 图表区域
        self.canvas = EnhancedMatplotlibCanvas()
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_label = QLabel("就绪")
        self.update_timer = QLabel("下次更新: --:--:--")
        self.status_bar.addPermanentWidget(self.status_label)
        self.status_bar.addPermanentWidget(self.update_timer)
        
        # 组装控制面板
        control_layout.addWidget(QLabel("股票代码:"), 0, 0)
        control_layout.addWidget(self.code_input, 0, 1)
        control_layout.addWidget(QLabel("刷新间隔:"), 0, 2)
        control_layout.addWidget(self.interval_combo, 0, 3)
        control_layout.addWidget(self.start_btn, 0, 4)
        control_layout.addWidget(self.stop_btn, 0, 5)
        control_layout.addWidget(self.save_btn, 0, 6)
        control_group.setLayout(control_layout)
        
        # 主布局组装
        main_layout.addWidget(control_group)
        main_layout.addWidget(data_group)
        main_layout.addWidget(self.canvas)
        
        # 样式设置
        self.setStyleSheet("""
            QGroupBox { 
                border: 1px solid #3A3939; 
                border-radius: 4px; 
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                color: #FFA500;
            }
            QPushButton {
                min-width: 80px;
                padding: 5px;
                background: #505050;
                color: white;
                border-radius: 3px;
            }
            QPushButton:hover {
                background: #606060;
            }
            QLabel {
                color: #FFA500;
            }
        """)

    def create_data_label(self, text):
        label = QLabel(text)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #FFA500;
            background: #FFFFFF;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #808080;
        """)
        label.setMinimumWidth(120)
        return label

    def update_values(self, data):
        analysis = self.query.analyze_dadan_trend(data)
        current = analysis['current_value'] / 10000
        
        # Update current value
        self.current_value.setText(f"{current:.2f}万")
        
        # Calculate difference
        if self.previous_value is not None:
            difference = current - self.previous_value
            difference_text = f"差值: {difference:+.2f}万"
            
            # Highlight if difference exceeds 1 million
            if abs(difference) > 100:
                self.difference_label.setStyleSheet("""
                    background-color: #FF0000;
                    color: #FFFFFF;
                    font-weight: bold;
                    border: 2px solid #880000;
                """)
            else:
                self.difference_label.setStyleSheet("""
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #808080;
                """)
                
            self.difference_label.setText(difference_text)
        else:
            self.difference_label.setText("差值: --")
            self.difference_label.setStyleSheet("""
                background-color: #FFFFFF;
                color: #000000;
                border: 1px solid #808080;
            """)
        
        # Store current value for next comparison
        self.previous_value = current
        
        # Update max/min values
        self.max_value.setText(f"{analysis['max_value']/10000:.2f}万")
        self.min_value.setText(f"{analysis['min_value']/10000:.2f}万")

        
    def start_query(self):
        self.stock_code = self.code_input.text().strip()
        if len(self.stock_code) != 6:
            QMessageBox.critical(self, "错误", "请输入有效的6位股票代码")
            return
        
        interval_map = [5, 10, 30, 60, 300]
        interval = interval_map[self.interval_combo.currentIndex()]
        
        self.timer.start(interval * 1000)
        self.countdown_timer.start(1000)
        self.toggle_controls(False)
        self.status_bar.showMessage(f"开始监控股票 {self.stock_code}...")
        self.update_data()

    def update_data(self):
        try:
            data = self.query.query_stock_l2_data(self.stock_code)
            if data:
                self.update_chart(data)
                self.update_values(data)
                self.status_bar.showMessage("数据更新成功", 5000)
            else:
                self.status_bar.showMessage("数据获取失败", 5000)
        except Exception as e:
            self.status_bar.showMessage(f"错误: {str(e)}", 5000)
        finally:
            self.reset_countdown()

    def update_chart(self, data):
        times, values, date_str = self.query.get_chart_data(data)
        if not times or not values:
            return
        
        self.canvas.ax.clear()
        
        # 绘制主曲线
        self.canvas.ax.plot(times, values, 'b-', linewidth=1.5, label='大单净额')
        
        # 填充区域
        self.canvas.ax.fill_between(times, values, 0, 
                                  where=(np.array(values) > 0),
                                  color='red', alpha=0.2)
        self.canvas.ax.fill_between(times, values, 0,
                                  where=(np.array(values) <= 0),
                                  color='green', alpha=0.2)
        
        # 标注和样式
        self.canvas.ax.set_title(f"股票 {self.stock_code} 大单净额走势 ({date_str})", 
                               fontproperties=self.canvas.font)
        self.canvas.ax.set_xlabel("时间", fontproperties=self.canvas.font)
        self.canvas.ax.set_ylabel("净额 (万元)", fontproperties=self.canvas.font)
        self.canvas.ax.grid(True, linestyle='--', alpha=0.6)
        self.canvas.ax.set_xticks(times[::10])
        self.canvas.ax.tick_params(axis='x', rotation=45)
        
        # 动态调整Y轴范围
        y_min, y_max = min(values)*1.1, max(values)*1.1
        self.canvas.ax.set_ylim(y_min if y_min < 0 else 0, y_max if y_max > 0 else 0)
        
        self.canvas.draw()


    def stop_query(self):
        self.timer.stop()
        self.countdown_timer.stop()
        self.toggle_controls(True)
        self.status_bar.showMessage("监控已停止")
        self.update_timer.setText("下次更新: --:--:--")

    def toggle_controls(self, enabled):
        self.code_input.setEnabled(enabled)
        self.interval_combo.setEnabled(enabled)
        self.start_btn.setEnabled(enabled)
        self.stop_btn.setEnabled(not enabled)
        self.save_btn.setEnabled(not enabled)

    def update_countdown(self):
        remaining = self.timer.remainingTime() // 1000
        mins, secs = divmod(remaining, 60)
        self.update_timer.setText(f"下次更新: {mins:02}:{secs:02}")

    def reset_countdown(self):
        self.countdown_timer.stop()
        self.countdown_timer.start(1000)

    def save_data(self):
        if hasattr(self, 'stock_code') and self.stock_code:
            data = self.query.query_stock_l2_data(self.stock_code)
            if data:
                filename = self.query.save_to_file(data, self.stock_code)
                self.status_bar.showMessage(f"数据已保存至 {filename}", 5000)
                return
        self.status_bar.showMessage("没有可保存的数据", 5000)

    def load_settings(self):
        self.code_input.setText(self.settings.value("stock_code", ""))
        self.interval_combo.setCurrentIndex(self.settings.value("interval_index", 1, type=int))

    def save_settings(self):
        self.settings.setValue("stock_code", self.code_input.text())
        self.settings.setValue("interval_index", self.interval_combo.currentIndex())

    def closeEvent(self, event):
        self.save_settings()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    window = StockL2App()
    window.show()
    sys.exit(app.exec_())