import pandas as pd
import dash
import dash_html_components as html
import dash_core_components as dcc
from dash.dependencies import Input, Output
from dash.exceptions import PreventUpdate
import dash_table



# 假设你已经有了DataFrame df

import requests

url = "https://apphis.longhuvip.com/w1/api/index.php"

querystring = {"a":"ChangeStatistics","st":"100","apiv":"w36","c":"HisHomeDingPan","PhoneOSNew":"1","UserID":"2075379","DeviceID":"e283305172a73233","VerSion":"********","Token":"94db1204a4623a18cef3d341bc72e712","Index":"0"}

headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; M2012K11AC Build/TKQ1.221114.001)",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Host": "apphis.longhuvip.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}

response = requests.request("POST", url, headers=headers, params=querystring)

print(response.text)
import pandas as pd
import json
data = json.loads(response.text)
df = pd.DataFrame(data["info"])

app = dash.Dash(__name__)

app.layout = html.Div([
    dcc.Input(id='input-rows', type='number', placeholder="Enter number of rows to display"),
    html.Div(id='table-container', children=[]),
])

@app.callback(
    Output('table-container', 'children'),
    [Input('input-rows', 'value')]
)
def update_table(n_rows):
    if n_rows is None:
        raise PreventUpdate
    else:
        return [
            dash_table.DataTable(
                id='table',
                columns=[{"name": i, "id": i} for i in df.columns],
                data=df.head(int(n_rows)).to_dict('records'),
                page_action='none',
                style_cell={'textAlign': 'left'},
            )
        ]

if __name__ == '__main__':
    app.run_server(debug=True)
