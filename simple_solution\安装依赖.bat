@echo off
title 安装依赖包
cd /d "%~dp0"
echo ===============================================
echo              安装依赖包
echo ===============================================
echo.
echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo 未找到Python！
    echo 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo.
echo Python环境正常
echo.
echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo 依赖包安装失败！
    echo 请检查网络连接或手动安装
    pause
    exit /b 1
)

echo.
echo 依赖包安装成功！
pause
