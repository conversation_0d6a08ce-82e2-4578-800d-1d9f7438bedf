#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理层 - 抽象基类和具体实现
支持异步请求、缓存和智能更新策略
"""

import sys
import time
import json
import threading
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMutex, QMutexLocker
import requests


class DataCache:
    """数据缓存类"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict] = {}
        self.access_times: Dict[str, datetime] = {}
        self.mutex = QMutex()
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with QMutexLocker(self.mutex):
            if key not in self.cache:
                return None
                
            # 检查是否过期
            if self._is_expired(key):
                self._remove(key)
                return None
                
            # 更新访问时间
            self.access_times[key] = datetime.now()
            return self.cache[key]['data']
            
    def set(self, key: str, data: Any) -> None:
        """设置缓存数据"""
        with QMutexLocker(self.mutex):
            # 如果缓存已满，删除最旧的数据
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
                
            self.cache[key] = {
                'data': data,
                'timestamp': datetime.now()
            }
            self.access_times[key] = datetime.now()
            
    def _is_expired(self, key: str) -> bool:
        """检查数据是否过期"""
        if key not in self.cache:
            return True
            
        timestamp = self.cache[key]['timestamp']
        return datetime.now() - timestamp > timedelta(seconds=self.ttl_seconds)
        
    def _remove(self, key: str) -> None:
        """删除缓存项"""
        if key in self.cache:
            del self.cache[key]
        if key in self.access_times:
            del self.access_times[key]
            
    def _evict_oldest(self) -> None:
        """删除最旧的缓存项"""
        if not self.access_times:
            return
            
        oldest_key = min(self.access_times.keys(), 
                        key=lambda k: self.access_times[k])
        self._remove(oldest_key)
        
    def clear(self) -> None:
        """清空缓存"""
        with QMutexLocker(self.mutex):
            self.cache.clear()
            self.access_times.clear()
            
    def size(self) -> int:
        """获取缓存大小"""
        with QMutexLocker(self.mutex):
            return len(self.cache)


class BaseDataManager(QObject):
    """数据管理基类"""
    
    # 信号定义
    data_updated = pyqtSignal(object)  # 数据更新信号
    error_occurred = pyqtSignal(str)   # 错误信号
    status_changed = pyqtSignal(str)   # 状态变化信号
    
    def __init__(self, cache_size: int = 100, cache_ttl: int = 60):
        super().__init__()
        self.cache = DataCache(cache_size, cache_ttl)
        self.is_active = False
        self.update_interval = 30  # 默认30秒更新间隔
        self.timer = QTimer()
        self.timer.timeout.connect(self._auto_update)
        self.last_update_time = None
        self.error_count = 0
        self.max_errors = 5
        
        # 请求会话（连接池）
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
        
        # 回调函数列表
        self.update_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
        
    def fetch_data(self, **kwargs) -> Optional[Any]:
        """获取数据（子类需要重写）"""
        raise NotImplementedError("子类必须实现fetch_data方法")

    def process_data(self, raw_data: Any) -> Any:
        """处理数据（子类需要重写）"""
        raise NotImplementedError("子类必须实现process_data方法")

    def get_cache_key(self, **kwargs) -> str:
        """获取缓存键（子类需要重写）"""
        raise NotImplementedError("子类必须实现get_cache_key方法")
        
    def start_auto_update(self, interval_seconds: int = None) -> None:
        """开始自动更新"""
        if interval_seconds:
            self.update_interval = interval_seconds
            
        self.is_active = True
        self.timer.start(self.update_interval * 1000)
        self.status_changed.emit("自动更新已启动")
        
        # 立即更新一次
        self._auto_update()
        
    def stop_auto_update(self) -> None:
        """停止自动更新"""
        self.is_active = False
        self.timer.stop()
        self.status_changed.emit("自动更新已停止")
        
    def update_data(self, force_refresh: bool = False, **kwargs) -> Optional[Any]:
        """更新数据"""
        cache_key = self.get_cache_key(**kwargs)
        
        # 如果不强制刷新，先尝试从缓存获取
        if not force_refresh:
            cached_data = self.cache.get(cache_key)
            if cached_data is not None:
                self.data_updated.emit(cached_data)
                return cached_data
                
        try:
            # 获取原始数据
            raw_data = self.fetch_data(**kwargs)
            if raw_data is None:
                self.error_occurred.emit("获取数据失败")
                return None
                
            # 处理数据
            processed_data = self.process_data(raw_data)
            
            # 缓存数据
            self.cache.set(cache_key, processed_data)
            
            # 更新状态
            self.last_update_time = datetime.now()
            self.error_count = 0
            
            # 发送信号
            self.data_updated.emit(processed_data)
            
            # 调用回调函数
            for callback in self.update_callbacks:
                try:
                    callback(processed_data)
                except Exception as e:
                    print(f"回调函数执行错误: {e}")
                    
            return processed_data
            
        except Exception as e:
            self.error_count += 1
            error_msg = f"数据更新错误: {str(e)}"
            self.error_occurred.emit(error_msg)
            
            # 调用错误回调
            for callback in self.error_callbacks:
                try:
                    callback(error_msg)
                except Exception as cb_e:
                    print(f"错误回调函数执行错误: {cb_e}")
                    
            # 如果错误次数过多，停止自动更新
            if self.error_count >= self.max_errors:
                self.stop_auto_update()
                self.status_changed.emit(f"错误次数过多，已停止自动更新")
                
            return None
            
    def _auto_update(self) -> None:
        """自动更新（内部方法）"""
        if self.is_active:
            self.update_data(force_refresh=True)
            
    def add_update_callback(self, callback: Callable) -> None:
        """添加数据更新回调函数"""
        if callback not in self.update_callbacks:
            self.update_callbacks.append(callback)
            
    def remove_update_callback(self, callback: Callable) -> None:
        """移除数据更新回调函数"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
            
    def add_error_callback(self, callback: Callable) -> None:
        """添加错误回调函数"""
        if callback not in self.error_callbacks:
            self.error_callbacks.append(callback)
            
    def remove_error_callback(self, callback: Callable) -> None:
        """移除错误回调函数"""
        if callback in self.error_callbacks:
            self.error_callbacks.remove(callback)
            
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'size': self.cache.size(),
            'max_size': self.cache.max_size,
            'ttl_seconds': self.cache.ttl_seconds,
            'last_update': self.last_update_time.isoformat() if self.last_update_time else None,
            'error_count': self.error_count,
            'is_active': self.is_active
        }
        
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.status_changed.emit("缓存已清空")
        
    def cleanup(self) -> None:
        """清理资源"""
        self.stop_auto_update()
        self.session.close()
        self.cache.clear()


class HotSearchDataManager(BaseDataManager):
    """热搜数据管理器"""
    
    def __init__(self):
        super().__init__(cache_size=50, cache_ttl=120)  # 2分钟缓存
        self.api_url = "https://apparticle.longhuvip.com/w1/api/index.php"
        self.api_params = {
            "a": "GetHotSearch",
            "apiv": "w31",
            "Type": "1",
            "c": "InteractData",
            "PhoneOSNew": "1",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******"
        }
        
    def fetch_data(self, **kwargs) -> Optional[List]:
        """获取热搜数据"""
        try:
            response = self.session.get(self.api_url, params=self.api_params, timeout=10)
            response.raise_for_status()
            return response.json().get('List', [])
        except Exception as e:
            print(f"热搜数据获取失败: {e}")
            return None
            
    def process_data(self, raw_data: List) -> List:
        """处理热搜数据"""
        if not raw_data:
            return []
            
        # 按热度值排序并添加排名
        sorted_data = sorted(raw_data, key=lambda x: x.get('Hot', 0), reverse=True)
        
        for idx, item in enumerate(sorted_data):
            item['Rank'] = idx + 1
            item['HotFormatted'] = self._format_hot_value(item.get('Hot', 0))
            
        return sorted_data
        
    def get_cache_key(self, **kwargs) -> str:
        """获取缓存键"""
        return "hot_search_data"
        
    def _format_hot_value(self, hot_value: int) -> str:
        """格式化热度值"""
        if hot_value >= 10000:
            return f"{hot_value/10000:.1f}万"
        else:
            return str(hot_value)


class StockL2DataManager(BaseDataManager):
    """股票L2数据管理器"""
    
    def __init__(self):
        super().__init__(cache_size=20, cache_ttl=30)  # 30秒缓存
        self.api_url = "https://apphq.longhuvip.com/w1/api/index.php"
        self.api_params = {
            "a": "GetStockDaDanTrendIncremental",
            "apiv": "w31",
            "c": "StockL2Data",
            "PhoneOSNew": "1",
            "UserID": "1410355",
            "DeviceID": "e283305172a73233",
            "VerSion": "*******",
            "Token": "e52a0e9702dc3ef939fe77835d2ea694"
        }
        
    def fetch_data(self, stock_code: str = None, **kwargs) -> Optional[Dict]:
        """获取股票L2数据"""
        if not stock_code:
            return None
            
        try:
            params = self.api_params.copy()
            params["StockID"] = stock_code
            
            response = self.session.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"股票L2数据获取失败: {e}")
            return None
            
    def process_data(self, raw_data: Dict) -> Dict:
        """处理股票L2数据"""
        if not raw_data or "dadanjinge" not in raw_data:
            return {}
            
        dadan_data = raw_data["dadanjinge"]
        if not dadan_data:
            return raw_data
            
        # 转换数据格式
        processed_data = raw_data.copy()
        
        # 计算统计信息
        values = [item[1] / 10000 for item in dadan_data]  # 转换为万元
        
        if values:
            processed_data['statistics'] = {
                'current_value': values[-1],
                'max_value': max(values),
                'min_value': min(values),
                'avg_value': sum(values) / len(values),
                'total_change': values[-1] - values[0] if len(values) > 1 else 0,
                'data_points': len(values)
            }
            
            # 添加趋势分析
            if len(values) >= 2:
                recent_trend = values[-1] - values[-2]
                processed_data['statistics']['recent_trend'] = recent_trend
                processed_data['statistics']['trend_direction'] = 'up' if recent_trend > 0 else 'down' if recent_trend < 0 else 'flat'
                
        return processed_data
        
    def get_cache_key(self, stock_code: str = None, **kwargs) -> str:
        """获取缓存键"""
        return f"stock_l2_{stock_code}" if stock_code else "stock_l2_default"


# 全局数据管理器实例
hot_search_manager = HotSearchDataManager()
stock_l2_manager = StockL2DataManager()


def get_hot_search_manager() -> HotSearchDataManager:
    """获取热搜数据管理器实例"""
    return hot_search_manager


def get_stock_l2_manager() -> StockL2DataManager:
    """获取股票L2数据管理器实例"""
    return stock_l2_manager


def cleanup_all_managers():
    """清理所有数据管理器"""
    hot_search_manager.cleanup()
    stock_l2_manager.cleanup()
