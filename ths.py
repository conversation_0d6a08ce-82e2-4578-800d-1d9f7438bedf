import tkinter as tk
from tkinter import messagebox
import requests
import json
import time
from datetime import datetime
import schedule
#同花顺
def fetch_data():
    url = "http://dq.10jqka.com.cn/fuyao/concept_express/new_concept/v1/list"
    querystring = {"page_num": "1", "page_size": "15"}
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.54 Mobile Safari/537.36 Hexin_Gphone/11.04.02 (Royal Flush) hxtheme/0 innerversion/G037.08.920.1.32 followPhoneSystemTheme/0 userid/423324878 getHXAPPAccessibilityMode/0 hxNewFont/1 isVip/0 getHXAPPFontSetting/small getHXAPPAdaptOldSetting/0",
        "Host": "dq.10jqka.com.cn",
        "Connection": "keep-alive",
        "sec-ch-ua": "\"Chromium\";v=\"124\", \"Android WebView\";v=\"124\", \"Not-A.Brand\";v=\"99\"",
        "Accept": "application/json, text/plain, */*",
        "withCreditials": "true",
        "Content-Type": "application/json",
        "sec-ch-ua-mobile": "?1",
        "sec-ch-ua-platform": "\"Android\"",
        "Origin": "https://eq.10jqka.com.cn",
        "X-Requested-With": "com.hexin.plat.android",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://eq.10jqka.com.cn/",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Cookie": "user=MDptb180MjMzMjQ4Nzg6Ok5vbmU6NTAwOjQzMzMyNDg3ODo3LDExMTExMTExMTExLDQwOzQ0LDExLDQwOzYsMSw0MDs1LDEsNDA7MSwxMDEsNDA7MiwxLDQwOzMsMSw0MDs1LDEsNDA7OCwwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMSw0MDsxMDIsMSw0MDoyNzo6OjQyMzMyNDg3ODoxNzE1MTQ3MjM4Ojo6MTUxMDU1MTA2MDoyNjc4NDAwOjA6MTBlZDJiZTFhMzkwMTliODE0ZTFhZjI1YjdiZGJjNjZiOjox; userid=423324878; u_name=mo_423324878; escapename=mo_423324878; ticket=ffb9f99a6ab9fcb8c513e9de3fe98dc0; user_status=0; IFUserCookieKey={\"escapename\":\"mo_423324878\",\"userid\":\"423324878\"}; hxmPid=ths_mob_gainiansudi; v=A4YodNA6HK58E8jKEjrCut7Y1ncI58qpnCn-BXCvciCFGikt2HcasWy7ThND"
    }

    try:
        response = requests.request("GET", url, headers=headers, params=querystring)
        response.raise_for_status()  # 检查请求是否成功
        data = response.json()
        total = data['data']['total']
        new_concepts = data['data']['new_concept']
        return total, new_concepts
    except requests.RequestException as e:
        messagebox.showerror("错误", f"数据获取失败: {e}")
        return None, None

def display_data(total, new_concepts):
    if total is None or new_concepts is None:
        return

    window = tk.Tk()
    window.title("新闻概览")

    # 添加标题标签
    title_label = tk.Label(window, text="新闻概览", font=("Arial", 18))
    title_label.pack(pady=10)

    # 使用框架组织内容
    content_frame = tk.Frame(window)
    content_frame.pack(fill="both", expand=True)

    text_box = tk.Text(content_frame, wrap="word", width=80, height=20)
    text_box.pack(fill="both", expand=True)

    for idx, concept in enumerate(new_concepts, start=1):
        text_box.insert(tk.END, f"\n\n{idx}. {concept['reason']}\n\n")
        text_box.insert(tk.END, f"概念: {concept['concept']['name']}\n")
        text_box.insert(tk.END, f"创建时间: {datetime.fromtimestamp(concept['created_at'] / 1000.0)}\n")
        text_box.insert(tk.END, f"链接: {concept['news_url']}\n\n")

    # 自定义按钮样式
    button_font = ("Arial", 12)
    button_bg = "#3F51B5"
    button_fg = "white"
    button_padx = 10
    button_pady = 5

    close_button = tk.Button(window, text="关闭", command=window.destroy, font=button_font, bg=button_bg, fg=button_fg, padx=button_padx, pady=button_pady)
    close_button.pack(side="bottom", pady=10)

    window.mainloop()


def job():
    total, new_concepts = fetch_data()
    if total and new_concepts:
        display_data(total, new_concepts)


def main():
    # 提前执行一次job，显示初始数据
    job()

    schedule.every(20).seconds.do(job)  # 每20秒执行一次job函数
    while True:
        schedule.run_pending()  # 运行即将到期的任务
        time.sleep(1)  # 避免CPU占用过高，稍微休眠一下


if __name__ == "__main__":
    main()

#    pyinstaller --onefile main.py