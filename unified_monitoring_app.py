#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一监控应用 - 高性能版本
整合热搜监控和股票L2监控功能
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget,
                            QVBoxLayout, QHBoxLayout, QStatusBar, QLabel,
                            QMenuBar, QAction, QMessageBox, QSplashScreen)
from PyQt5.QtCore import Qt, QTimer, QSettings, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QFont
from datetime import datetime
import json

# 导入性能优化模块
try:
    from performance_optimizer import (initialize_performance_optimization,
                                     cleanup_performance_optimization,
                                     memory_manager, profiler)
    PERFORMANCE_OPTIMIZATION_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATION_AVAILABLE = False

# 导入现代化主题
try:
    from modern_theme import apply_modern_theme, ModernTheme, ModernEffects
    MODERN_THEME_AVAILABLE = True
except ImportError:
    MODERN_THEME_AVAILABLE = False

class UnifiedMonitoringApp(QMainWindow):
    """统一监控应用主窗口"""
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings('UnifiedMonitor', 'Settings')

        # 初始化性能优化
        if PERFORMANCE_OPTIMIZATION_AVAILABLE:
            initialize_performance_optimization()
            # 连接内存警告信号
            memory_manager.memory_warning.connect(self.on_memory_warning)

        self.init_ui()
        self.setup_status_bar()
        self.setup_menu_bar()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📊 统一监控应用 - 现代化版本")
        self.setGeometry(100, 100, 1400, 900)

        # 设置应用图标
        self.setWindowIcon(self.create_app_icon())

        # 应用现代化主题
        if MODERN_THEME_AVAILABLE:
            # 主题样式将在main函数中应用
            pass

        # 创建主容器
        main_container = QWidget()
        self.setCentralWidget(main_container)
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 创建标题区域
        self.create_header_section(main_layout)

        # 创建中央标签页控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 连接标签页切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # 初始化组件（延迟加载）
        self.hot_search_widget = None
        self.stock_l2_widget = None

        # 创建占位符标签页
        self.create_placeholder_tabs()

        # 添加阴影效果
        if MODERN_THEME_AVAILABLE:
            ModernEffects.add_shadow(self.tab_widget, blur_radius=20, offset=(0, 4))

    def create_header_section(self, layout):
        """创建标题区域"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # 应用标题
        title_label = QLabel("📊 统一监控应用")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: 700;
                color: {ModernTheme.PRIMARY if MODERN_THEME_AVAILABLE else '#2196F3'};
                margin: 0;
                padding: 0;
            }}
        """)

        # 副标题
        subtitle_label = QLabel("实时数据监控 • 高性能 • 现代化界面")
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_SECONDARY if MODERN_THEME_AVAILABLE else '#757575'};
                margin-top: 4px;
            }}
        """)

        # 状态指示器
        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                color: {ModernTheme.SUCCESS if MODERN_THEME_AVAILABLE else '#4CAF50'};
                margin-left: 8px;
            }}
        """)

        # 布局
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        header_layout.addWidget(title_container)
        header_layout.addStretch()
        header_layout.addWidget(QLabel("状态:"))
        header_layout.addWidget(self.status_indicator)
        header_layout.addWidget(QLabel("运行中"))

        layout.addWidget(header_widget)
        
    def create_app_icon(self):
        """创建应用图标"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制简单的图标
        painter.setBrush(Qt.blue)
        painter.drawEllipse(4, 4, 24, 24)
        
        painter.setBrush(Qt.white)
        painter.drawEllipse(8, 8, 16, 16)
        
        painter.end()
        return QIcon(pixmap)
        
    def create_placeholder_tabs(self):
        """创建占位符标签页"""
        # 热搜监控标签页
        hot_search_placeholder = self.create_loading_placeholder(
            "🔥", "热搜监控", "实时监控热搜关键词数据", ModernTheme.ERROR if MODERN_THEME_AVAILABLE else "#FF5722"
        )
        self.tab_widget.addTab(hot_search_placeholder, "🔥 热搜监控")

        # 股票L2监控标签页
        stock_placeholder = self.create_loading_placeholder(
            "📈", "股票L2监控", "监控股票大单净额数据", ModernTheme.SUCCESS if MODERN_THEME_AVAILABLE else "#4CAF50"
        )
        self.tab_widget.addTab(stock_placeholder, "📈 股票L2监控")

    def create_loading_placeholder(self, icon, title, description, color):
        """创建美观的加载占位符"""
        placeholder = QWidget()
        layout = QVBoxLayout(placeholder)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(20)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 64px;
                color: {color};
                margin-bottom: 10px;
            }}
        """)

        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY if MODERN_THEME_AVAILABLE else '#212121'};
                margin-bottom: 5px;
            }}
        """)

        # 描述
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_SECONDARY if MODERN_THEME_AVAILABLE else '#757575'};
                margin-bottom: 20px;
            }}
        """)

        # 加载动画（简单的文字提示）
        loading_label = QLabel("正在加载模块...")
        loading_label.setAlignment(Qt.AlignCenter)
        loading_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                color: {ModernTheme.TEXT_HINT if MODERN_THEME_AVAILABLE else '#BDBDBD'};
                font-style: italic;
            }}
        """)

        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addWidget(loading_label)

        return placeholder
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 内存使用标签
        self.memory_label = QLabel("内存: --MB")
        self.status_bar.addPermanentWidget(self.memory_label)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 更新时间显示
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        refresh_action = QAction('刷新当前页(&R)', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_current_tab)
        view_menu.addAction(refresh_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def on_tab_changed(self, index):
        """标签页切换事件"""
        # 添加切换动画效果
        if MODERN_THEME_AVAILABLE:
            self.animate_tab_switch(index)

        if index == 0 and self.hot_search_widget is None:
            # 延迟加载热搜监控组件
            self.load_hot_search_widget()
        elif index == 1 and self.stock_l2_widget is None:
            # 延迟加载股票L2监控组件
            self.load_stock_l2_widget()

        # 确保状态栏已初始化
        if hasattr(self, 'status_label'):
            self.update_status(f"切换到: {self.tab_widget.tabText(index)}")

    def animate_tab_switch(self, index):
        """标签页切换动画"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve

            # 获取当前标签页内容
            current_widget = self.tab_widget.currentWidget()
            if current_widget:
                # 创建淡入动画
                self.fade_animation = QPropertyAnimation(current_widget, b"windowOpacity")
                self.fade_animation.setDuration(200)
                self.fade_animation.setStartValue(0.7)
                self.fade_animation.setEndValue(1.0)
                self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
                self.fade_animation.start()
        except ImportError:
            pass  # 如果动画不可用，跳过
        
    def load_hot_search_widget(self):
        """加载热搜监控组件"""
        try:
            from hot_search_widget import HotSearchWidget
            from data_manager import get_hot_search_manager

            self.hot_search_widget = HotSearchWidget()

            # 连接数据管理器
            hot_search_manager = get_hot_search_manager()
            hot_search_manager.data_updated.connect(self.hot_search_widget.on_data_received)
            hot_search_manager.error_occurred.connect(self.hot_search_widget.on_error_occurred)
            hot_search_manager.status_changed.connect(self.update_status)

            self.tab_widget.removeTab(0)
            self.tab_widget.insertTab(0, self.hot_search_widget, "🔥 热搜监控")
            self.tab_widget.setCurrentIndex(0)
            self.update_status("热搜监控模块已加载")

        except ImportError as e:
            self.update_status(f"热搜监控模块加载失败: {str(e)}")

    def load_stock_l2_widget(self):
        """加载股票L2监控组件"""
        try:
            from stock_l2_widget import StockL2Widget
            from data_manager import get_stock_l2_manager

            self.stock_l2_widget = StockL2Widget()

            # 连接数据管理器
            stock_l2_manager = get_stock_l2_manager()
            stock_l2_manager.data_updated.connect(self.stock_l2_widget.on_data_received)
            stock_l2_manager.error_occurred.connect(self.stock_l2_widget.on_error_occurred)
            stock_l2_manager.status_changed.connect(self.update_status)

            self.tab_widget.removeTab(1)
            self.tab_widget.insertTab(1, self.stock_l2_widget, "📈 股票L2监控")
            self.tab_widget.setCurrentIndex(1)
            self.update_status("股票L2监控模块已加载")

        except ImportError as e:
            self.update_status(f"股票L2监控模块加载失败: {str(e)}")
            
    def update_status(self, message):
        """更新状态栏消息"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
        else:
            print(f"状态: {message}")  # 如果状态栏未初始化，打印到控制台
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"时间: {current_time}")
        
        # 更新内存使用情况
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.memory_label.setText(f"内存: {memory_mb:.1f}MB")
        except ImportError:
            pass
            
    def refresh_current_tab(self):
        """刷新当前标签页"""
        current_index = self.tab_widget.currentIndex()
        current_widget = self.tab_widget.currentWidget()
        
        if hasattr(current_widget, 'refresh_data'):
            current_widget.refresh_data()
            self.update_status("数据已刷新")
        else:
            self.update_status("当前页面不支持刷新")
            
    def export_data(self):
        """导出数据"""
        current_widget = self.tab_widget.currentWidget()
        
        if hasattr(current_widget, 'export_data'):
            current_widget.export_data()
        else:
            QMessageBox.information(self, "提示", "当前页面不支持数据导出")
            
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "统一监控应用 v1.0\n\n"
                         "整合热搜监控和股票L2监控功能\n"
                         "高性能、低延时设计\n\n"
                         "技术栈: PyQt5, matplotlib, requests")
                         
    def load_settings(self):
        """加载设置"""
        # 恢复窗口位置和大小
        geometry = self.settings.value('geometry')
        if geometry:
            self.restoreGeometry(geometry)
            
        # 恢复当前标签页
        current_tab = self.settings.value('current_tab', 0, type=int)
        if 0 <= current_tab < self.tab_widget.count():
            self.tab_widget.setCurrentIndex(current_tab)
            
    def save_settings(self):
        """保存设置"""
        self.settings.setValue('geometry', self.saveGeometry())
        self.settings.setValue('current_tab', self.tab_widget.currentIndex())
        
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()

        # 停止所有组件的定时器
        if self.hot_search_widget and hasattr(self.hot_search_widget, 'stop_timers'):
            self.hot_search_widget.stop_timers()

        if self.stock_l2_widget and hasattr(self.stock_l2_widget, 'stop_timers'):
            self.stock_l2_widget.stop_timers()

        # 清理数据管理器
        try:
            from data_manager import cleanup_all_managers
            cleanup_all_managers()
        except ImportError:
            pass

        # 清理性能优化
        if PERFORMANCE_OPTIMIZATION_AVAILABLE:
            cleanup_performance_optimization()

        event.accept()

    def on_memory_warning(self, memory_mb):
        """内存警告处理"""
        self.update_status(f"内存使用警告: {memory_mb:.1f}MB")

        # 可以在这里添加内存清理逻辑
        if hasattr(self, 'hot_search_widget') and self.hot_search_widget:
            if hasattr(self.hot_search_widget, 'data_cache'):
                self.hot_search_widget.data_cache.clear()

        if hasattr(self, 'stock_l2_widget') and self.stock_l2_widget:
            if hasattr(self.stock_l2_widget, 'data_cache'):
                self.stock_l2_widget.data_cache = None


def create_splash_screen():
    """创建启动画面"""
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    painter = QPainter(splash_pix)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # 绘制标题
    font = QFont("Arial", 24, QFont.Bold)
    painter.setFont(font)
    painter.setPen(Qt.blue)
    painter.drawText(50, 100, "统一监控应用")
    
    # 绘制副标题
    font = QFont("Arial", 12)
    painter.setFont(font)
    painter.setPen(Qt.gray)
    painter.drawText(50, 130, "高性能实时数据监控")
    
    # 绘制版本信息
    painter.drawText(50, 250, "版本 1.0 - 正在启动...")
    
    painter.end()
    
    splash = QSplashScreen(splash_pix)
    splash.setMask(splash_pix.mask())
    return splash


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("统一监控应用")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("MonitoringApp")
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 显示启动画面
    splash = create_splash_screen()
    splash.show()
    app.processEvents()
    
    # 创建主窗口
    window = UnifiedMonitoringApp()
    
    # 隐藏启动画面并显示主窗口
    splash.finish(window)
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
