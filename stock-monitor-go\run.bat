@echo off
chcp 65001 >nul
title 轻量级股票监控程序

echo.
echo ████████████████████████████████████████
echo █                                      █
echo █     📊 轻量级股票监控程序 v1.0        █
echo █                                      █
echo █     🚀 超轻量级 ^| 高性能 ^| 跨平台     █
echo █                                      █
echo ████████████████████████████████████████
echo.

echo 🔍 检查程序文件...
if not exist "stock-monitor.exe" (
    echo ❌ 未找到 stock-monitor.exe
    echo 请先运行 build.bat 编译程序
    echo.
    pause
    exit /b 1
)

echo ✅ 程序文件检查通过
echo.

echo 🌐 启动股票监控服务...
echo.
echo 📊 服务信息:
echo    - 访问地址: http://localhost:8080
echo    - 内存占用: ^< 20MB
echo    - 启动时间: ^< 1秒
echo.
echo 💡 功能特性:
echo    - 🔥 实时热搜监控
echo    - 📈 股票L2数据查询
echo    - 🔄 自动刷新功能
echo    - 📱 响应式Web界面
echo.

echo ⚡ 正在启动服务器...
echo.
echo ========================================
echo 服务器日志输出:
echo ========================================

REM 启动程序
stock-monitor.exe

echo.
echo ========================================
echo 服务器已停止运行
echo ========================================
echo.
pause
