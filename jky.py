import requests

url = "https://www.newhealth.com.cn/checkup/report/jcbgpdf"

querystring = {"path":"cvOtrgfhneKRQ02jnanhdq5+ijHbHbot4cLTnc+E7fk5+bAR37by5MNfjdBhT2+gTywUGN//MPFXpuUYI/h5aBuYFihintNhYjOe01Z+TEQ="}

headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/122.0.6261.120 Mobile Safari/537.36 XWEB/1220067 MMWEBSDK/20240404 MMWEBID/4990 MicroMessenger/8.0.49.2600(0x28003133) WeChat/arm64 Weixin NetType/5G Language/zh_CN ABI/arm64 miniProgram/wx430d371fb280bcd5",
    "Host": "www.newhealth.com.cn",
    "Connection": "keep-alive",
    "sec-ch-ua": "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Android WebView\";v=\"122\"",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "Accept": "*/*",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://www.newhealth.com.cn/checkup/js/pdfjs/web/viewer.html?file=/checkup/report/jcbgpdf?path=q7NmjmUiNu%2B4kE7xQyGKDna1Mp34%2Bm6hnG8hzMreQ3ZtXuGSY74EQFMhzlNhfBDkm%2F6lDwivhA234Y79cWLSLzt5vZ0SyIJnfVJ%2BAKh6Ehmc6TZfcUH4nE%2F8z9iTQOalHStnM86xNq7rvRFDDbBu2lhY56o8vgHEb05SwT5wGKtULoZ8e2Ijh66h4BTv3%2BgDZ2%2BZdY00Q7%2FKywFVjINaT%2FxwjTZ9sCrjYiPhx7yEuFxAtu8slGRazKdulVCSeX3p",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    "Cookie": "wzws_sessionid=gTM2YjhmZoI3MGQzMzeAMjQwOTo4OTVhOjM0Njk6MTFmNzo0YjBkOjczMzQ6NWVjNjo5M2EzoGZXSXw=; xkypsession=ZGU0ZjQ1YjgtZWYxNy00YmFkLTlkNDAtMTEzODAzMGVjMjhl; JSESSIONID=f09d2284-828f-4211-baef-fee08899edce"
}

response = requests.get(url, headers=headers, params=querystring)

pdf_data = response.content

with open("output.pdf", "wb") as f:
    f.write(pdf_data)

print("PDF downloaded and saved as output.pdf")

