import json
import requests
import tkinter as tk
from tkinter import ttk

def load_data():
    global data_items
    url = "https://www.szse.cn/api/report/ShowReport/data"
    querystring = {"SHOWTYPE":"JSON","CATALOGID":"main_wxhj","TABKEY":"tab3","random":"0.8969034716429898"}
    headers = {"User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******"}
    response = requests.request("GET", url, headers=headers, params=querystring)
    data_list = json.loads(response.text)
    data_list = data_list[1]
    data_items = [item for item in data_list['data']]
    for item in data_items:
        del item['hfck']

    # 获取新数据
    url2 = "http://szse.cn/api/report/ShowReport/data"
    querystring2 = {"SHOWTYPE":"JSON","CATALOGID":"main_wxhj","TABKEY":"tab2","random":"0.2871948982874386"}
    headers2 = {"User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******"}
    response2 = requests.request("GET", url2, headers=headers2, params=querystring2)
    data_list2 = json.loads(response2.text)
    data_list2 = data_list2[0]
    data_items2 = [item for item in data_list2['data']]
    for item in data_items2:
        del item['hfck']
    data_items.extend(data_items2)

    # 获取新数据
    url3 = "http://szse.cn/api/report/ShowReport/data"
    querystring3 = {"SHOWTYPE":"JSON","CATALOGID":"1800_jgxxgk","loading":"first","random":"0.051851769462360586"}
    headers3 = {"User-Agent": "Mozilla/5.0 (Linux; Android 11; M2012K11AC Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36;kaipanla *******"}
    response3 = requests.request("GET", url3, headers=headers3, params=querystring3)
    data_list3 = json.loads(response3.text)
    data_list3 = data_list3[0]
    data_items3 = [item for item in data_list3['data']]
    for item in data_items3:
        del item['gkxx_sjdx']

    key_mapping = {
        'gkxx_gsdm': 'gsdm',
        'gkxx_gsjc': 'gsjc',
        'gkxx_gdrq': 'fhrq',
        'gkxx_jgcs': 'hjlb',
        'hjnr': 'ck'
    }
    for item in data_items3:
        for old_key, new_key in key_mapping.items():
            if old_key in item:
                item[new_key] = item.pop(old_key)

    # 合并数据
    data_items.extend(data_items3)

    # 根据时间重新排序
    data_items.sort(key=lambda x: x['fhrq'], reverse=True)

    update_table()

def update_table():
    global data_items
    table.delete(*table.get_children())
    for item in data_items:
        table.insert("", "end", values=tuple(item.values()))

root = tk.Tk()
root.title("Data Items")

# 创建Scrollbar并放置在右侧
scrollbar = ttk.Scrollbar(root)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
# 创建Treeview，并关联Scrollbar
table = ttk.Treeview(root, columns=("代码", "名称", "时间", "类别", "链接"), show="headings")
table.heading("代码", text="代码")
table.heading("名称", text="名称")
table.heading("时间", text="时间")
table.heading("类别", text="类别")
table.heading("链接", text="链接")

# 设置每列的最大宽度
table.column("#1", width=60)
table.column("#2", width=60)
table.column("#3", width=90)
table.column("#4", width=80)
table.column("#5", width=10)
table.pack(fill=tk.BOTH, expand=True)

# 配置Scrollbar的command属性，使其能控制Treeview的滚动
scrollbar.config(command=table.yview)

load_button = tk.Button(root, text="Load Data", command=load_data)
load_button.pack()
load_data()
root.mainloop()
