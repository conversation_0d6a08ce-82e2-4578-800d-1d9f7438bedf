@echo off
chcp 65001 >nul
echo 📊 轻量级股票监控程序 - 构建脚本
echo =====================================

echo.
echo 🔍 检查Go环境...
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Go语言环境
    echo 请先安装Go语言: https://golang.org/dl/
    pause
    exit /b 1
)

echo ✅ Go环境检查通过

echo.
echo 📦 安装依赖包...
go mod tidy
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo.
echo 🔨 开始编译程序...

REM 编译当前平台版本
echo 正在编译 Windows 版本...
go build -ldflags="-s -w" -o stock-monitor.exe .
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ Windows 版本编译完成: stock-monitor.exe

echo.
echo 🌍 编译跨平台版本...

REM 编译Linux版本
echo 正在编译 Linux 版本...
set GOOS=linux
set GOARCH=amd64
go build -ldflags="-s -w" -o stock-monitor-linux .

REM 编译macOS版本
echo 正在编译 macOS 版本...
set GOOS=darwin
set GOARCH=amd64
go build -ldflags="-s -w" -o stock-monitor-macos .

REM 重置环境变量
set GOOS=
set GOARCH=

echo.
echo 📁 生成的文件:
dir /b stock-monitor*

echo.
echo 🎉 构建完成！
echo.
echo 💡 使用方法:
echo    1. 双击 stock-monitor.exe 启动程序
echo    2. 打开浏览器访问 http://localhost:8080
echo    3. 开始使用股票监控功能
echo.
echo 📊 程序特点:
echo    - 占用内存: ^< 20MB
echo    - 启动时间: ^< 1秒
echo    - 文件大小: 5-10MB
echo    - 跨平台支持
echo.

pause
