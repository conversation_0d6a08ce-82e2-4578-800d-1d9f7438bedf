import requests

headers = {
    'Accept': 'application/json',
    'Accept-Encoding': 'gzip, deflate, br',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Cookie': 'ct=wqu8Z6R3Nf3Xnqcl7wY-x29ct0JYJhJxgP2TQWk7JsbSbKiH3_Tm_RX5mJqMhulj6HyXH0hWtIzm28Sipc65srjP38e3fGKQ6saC5W8SDUFqQDHyitmvpj031o07WY_HxgPGYMQ972FTVW9sZNsO1HAVzp-dl9zEE_LW5XzmGn4; ut=FobyicMgeV4FyBMMJQq2iJwMXtBwATQ3iMgGXgruxmId8kheAAxMfp1xn6uIygDRXWcudYYSxLnoc3Ge-9PixKDxvO6UB3YwreEwvFUDeSDoPvUUOFnygw1PiwugApoEn68RKgROMSgksCadgxE3DyCxQJNa0N3RR-z1tYgWcOejr8itx9aa-IWkqycw0acDFe1mYprZKgn0ECazYw8d5Q7Bn-kY4Em1b3oXDWo_I_m-NCDoH5f1ZSTSz_vLWPZShvZFmReoLh2COUI9MkXBk1mp_PyeWOcz2mSXysMR9F_NBjlzNfc314i5hP7M9qwNYSwEDfvedSSqnha-AZm_5g; pi=5261047042954600;gxhk007659;股友R3291P5766;6ooBYMpgrnJgMogaM5ybo4RWORg+zQsME4Q+VapO0nt3NfdkGX3IGK0HM/2tUODQhg0wKpOMsLu9NqsjJKfJcUwTC39hpRqEmbHGWI6ZdwTZzN7rB4+HUKJYbye2wkX9Z6hZOTdK24Ja9aR9bYeNlHHkvClHnPzCWS2t/1IG00VWYGUCHov2XjLu3SqS794NBY0GMoGe;qFvJD6PlENY/rpa6Qulmj3W8eA79Yek5S23x8k9Bdrko7EIVkQDBfw9tCqbXdQV1MdLVvOvH4z7+S4YdPSdrv4RkAtkW5fgJFi/CRxZ1P5u6u4ySa5Z6Siy1R37v70Na7krzvThISCCnpf0Hd8WHPnP96fvjCA==; uidal=5261047042954600股友R3291P5766',
    'Origin': 'https://choicewh2.eastmoney.com',
    'Referer': 'https://choicewh2.eastmoney.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36',
    'appid': 'UHc7QTQqgQe0JtUsK7cdWaBIrRJYmmsJ@MOBILEAPP',
    'sec-ch-ua-platform': 'Windows'
}

data = {
    "uid": "5261047042954600",
    "topType": "0",
    "liveState": ["2"],
    "industryClass": ["0"],
    "contentType": ["0"],
    "authority": ["0"],
    "pageNo": 1,
    "pageSize": 50
}

response = requests.post('https://choicegw2.eastmoney.com/langke/interface/langke/luyanListSearch', headers=headers, json=data)

print(response.json())