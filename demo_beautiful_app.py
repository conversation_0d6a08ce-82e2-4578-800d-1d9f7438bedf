#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美化版统一监控应用演示
展示现代化界面设计效果
"""

import sys
from PyQt5.QtWidgets import QApplication

def main():
    """启动美化版应用"""
    print("🎨 启动美化版统一监控应用...")
    
    try:
        # 导入美化版应用
        from run_unified_app import main as run_main
        
        # 直接运行
        run_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有文件都在同一目录下")
        
    except Exception as e:
        print(f"❌ 启动错误: {e}")

if __name__ == "__main__":
    main()
