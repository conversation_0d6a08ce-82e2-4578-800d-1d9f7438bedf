<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            font-size: 1.8rem;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .input {
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .status.success {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status.error {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .status.loading {
            background: #bee3f8;
            color: #2a4365;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .data-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }
        
        .data-table tr:hover {
            background: #f7fafc;
        }
        
        .hot-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .hot-item:last-child {
            border-bottom: none;
        }
        
        .hot-keyword {
            font-weight: 600;
            color: #2d3748;
        }
        
        .hot-value {
            color: #e53e3e;
            font-weight: bold;
        }
        
        .order-change {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .order-up {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .order-down {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .order-same {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 轻量级股票监控</h1>
            <p>实时热搜数据 & 股票L2监控 | 基于Go语言开发</p>
        </div>
        
        <div class="dashboard">
            <!-- 热搜监控 -->
            <div class="card">
                <h2><span class="icon">🔥</span>热搜监控</h2>
                <div class="controls">
                    <button class="btn" onclick="loadHotSearch()">刷新数据</button>
                    <button class="btn" id="autoRefreshBtn" onclick="toggleAutoRefresh()">开启自动刷新</button>
                </div>
                <div id="hotSearchStatus" class="status loading">正在加载数据...</div>
                <div id="hotSearchData"></div>
            </div>
            
            <!-- 股票L2监控 -->
            <div class="card">
                <h2><span class="icon">📈</span>股票L2监控</h2>
                <div class="controls">
                    <input type="text" id="stockCode" class="input" placeholder="输入股票代码 (如: 002334)" maxlength="6">
                    <button class="btn" onclick="loadStockData()">查询数据</button>
                </div>
                <div id="stockStatus" class="status">请输入股票代码查询数据</div>
                <div id="stockData"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>💡 轻量级设计 | 低资源占用 | 实时数据更新</p>
            <p>服务状态: <span id="serverStatus">检查中...</span></p>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
            loadHotSearch();
            
            // 股票代码输入框回车事件
            document.getElementById('stockCode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadStockData();
                }
            });
        });

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();
                document.getElementById('serverStatus').textContent = '✅ 正常运行';
                document.getElementById('serverStatus').style.color = '#22543d';
            } catch (error) {
                document.getElementById('serverStatus').textContent = '❌ 连接失败';
                document.getElementById('serverStatus').style.color = '#742a2a';
            }
        }

        // 加载热搜数据
        async function loadHotSearch() {
            const statusEl = document.getElementById('hotSearchStatus');
            const dataEl = document.getElementById('hotSearchData');
            
            statusEl.className = 'status loading';
            statusEl.innerHTML = '<span class="loading"></span> 正在获取热搜数据...';
            
            try {
                const response = await fetch('/api/v1/hotsearch');
                const result = await response.json();
                
                if (result.success && result.data) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 数据更新成功 (${result.count}条) - ${new Date().toLocaleTimeString()}`;
                    
                    // 渲染热搜数据
                    let html = '';
                    result.data.slice(0, 10).forEach((item, index) => {
                        const orderClass = item.Order > 0 ? 'order-up' : item.Order < 0 ? 'order-down' : 'order-same';
                        const orderText = item.Order > 0 ? `↑${item.Order}` : item.Order < 0 ? `↓${Math.abs(item.Order)}` : '-';
                        
                        html += `
                            <div class="hot-item">
                                <div>
                                    <span style="color: #666; margin-right: 8px;">${index + 1}.</span>
                                    <span class="hot-keyword">${item.KeyWord}</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <span class="hot-value">${item.Hot.toLocaleString()}</span>
                                    <span class="order-change ${orderClass}">${orderText}</span>
                                </div>
                            </div>
                        `;
                    });
                    dataEl.innerHTML = html;
                } else {
                    throw new Error('数据格式错误');
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 获取数据失败: ' + error.message;
                dataEl.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无数据</p>';
            }
        }

        // 加载股票数据
        async function loadStockData() {
            const stockCode = document.getElementById('stockCode').value.trim();
            const statusEl = document.getElementById('stockStatus');
            const dataEl = document.getElementById('stockData');
            
            if (!stockCode) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 请输入股票代码';
                return;
            }
            
            if (!/^\d{6}$/.test(stockCode)) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 股票代码格式错误，请输入6位数字';
                return;
            }
            
            statusEl.className = 'status loading';
            statusEl.innerHTML = '<span class="loading"></span> 正在获取股票数据...';
            
            try {
                const response = await fetch(`/api/v1/stock/${stockCode}`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 股票 ${stockCode} 数据获取成功 - ${new Date().toLocaleTimeString()}`;
                    
                    // 渲染股票数据
                    let html = '<div style="background: #f7fafc; padding: 15px; border-radius: 8px;">';
                    html += `<h3 style="margin-bottom: 10px; color: #2d3748;">股票代码: ${stockCode}</h3>`;
                    
                    if (result.data.dadanjinge) {
                        html += '<p><strong>大单净额数据:</strong></p>';
                        html += `<pre style="background: white; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(result.data.dadanjinge, null, 2)}</pre>`;
                    } else {
                        html += '<p style="color: #666;">暂无大单净额数据</p>';
                    }
                    
                    html += '</div>';
                    dataEl.innerHTML = html;
                } else {
                    throw new Error('数据格式错误或股票代码无效');
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 获取数据失败: ' + error.message;
                dataEl.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无数据</p>';
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (isAutoRefreshing) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshing = false;
                btn.textContent = '开启自动刷新';
                btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            } else {
                autoRefreshInterval = setInterval(loadHotSearch, 10000); // 10秒刷新一次
                isAutoRefreshing = true;
                btn.textContent = '关闭自动刷新';
                btn.style.background = 'linear-gradient(135deg, #e53e3e 0%, #fd5e53 100%)';
            }
        }
    </script>
</body>
</html>
